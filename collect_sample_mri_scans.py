"""
Collect 25 sample MRI scans with proper metadata naming for demo testing
Format: [Condition]_age_[Age]_[Gender]_[ID].nii
"""

import os
import shutil
import random
from pathlib import Path
import nibabel as nib
import numpy as np

def create_sample_collection():
    """Create a collection of 25 sample MRI scans with proper metadata naming"""
    
    # Source directory (test data from Windows Z: drive)
    source_dir = "/mnt/z/s3-unzipped"  # WSL2 mount point for Z: drive
    
    # Target directory for sample collection
    target_dir = "/mnt/z/demo_mri_samples_25"
    
    print("🧠 Collecting 25 Sample MRI Scans for Demo Testing")
    print("=" * 60)
    
    # Create target directory
    os.makedirs(target_dir, exist_ok=True)
    
    # Sample metadata for realistic naming
    sample_cases = [
        # Cognitively Normal (CN) cases
        {"condition": "CN", "age": 65, "gender": "M", "id": "001"},
        {"condition": "CN", "age": 72, "gender": "F", "id": "002"},
        {"condition": "CN", "age": 58, "gender": "M", "id": "003"},
        {"condition": "CN", "age": 69, "gender": "F", "id": "004"},
        {"condition": "CN", "age": 61, "gender": "M", "id": "005"},
        {"condition": "CN", "age": 74, "gender": "F", "id": "006"},
        {"condition": "CN", "age": 67, "gender": "M", "id": "007"},
        {"condition": "CN", "age": 63, "gender": "F", "id": "008"},
        
        # Mild Cognitive Impairment (MCI) cases
        {"condition": "MCI", "age": 71, "gender": "F", "id": "009"},
        {"condition": "MCI", "age": 68, "gender": "M", "id": "010"},
        {"condition": "MCI", "age": 75, "gender": "F", "id": "011"},
        {"condition": "MCI", "age": 73, "gender": "M", "id": "012"},
        {"condition": "MCI", "age": 70, "gender": "F", "id": "013"},
        {"condition": "MCI", "age": 76, "gender": "M", "id": "014"},
        {"condition": "MCI", "age": 69, "gender": "F", "id": "015"},
        {"condition": "MCI", "age": 72, "gender": "M", "id": "016"},
        {"condition": "MCI", "age": 74, "gender": "F", "id": "017"},
        
        # Alzheimer's Disease (AD) cases
        {"condition": "AD", "age": 78, "gender": "M", "id": "018"},
        {"condition": "AD", "age": 81, "gender": "F", "id": "019"},
        {"condition": "AD", "age": 76, "gender": "M", "id": "020"},
        {"condition": "AD", "age": 79, "gender": "F", "id": "021"},
        {"condition": "AD", "age": 82, "gender": "M", "id": "022"},
        {"condition": "AD", "age": 77, "gender": "F", "id": "023"},
        {"condition": "AD", "age": 80, "gender": "M", "id": "024"},
        {"condition": "AD", "age": 83, "gender": "F", "id": "025"},
    ]
    
    print(f"📁 Source directory: {source_dir}")
    print(f"📁 Target directory: {target_dir}")
    print(f"🎯 Target: 25 scans (8 CN, 9 MCI, 8 AD)")
    print()
    
    # Check if source directory exists
    if not os.path.exists(source_dir):
        print(f"❌ Source directory not found: {source_dir}")
        print("   Please ensure Z: drive is mounted and contains test data")
        return False
    
    # Find available MRI files
    print("🔍 Searching for MRI files...")
    mri_files = []
    
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.endswith(('.nii', '.nii.gz')):
                full_path = os.path.join(root, file)
                mri_files.append(full_path)
    
    print(f"   Found {len(mri_files)} MRI files")
    
    if len(mri_files) < 25:
        print(f"⚠️  Only {len(mri_files)} files found, need at least 25")
        print("   Will create synthetic samples if needed")
    
    # Randomly select and copy files with proper naming
    selected_files = random.sample(mri_files, min(25, len(mri_files)))
    
    print("\n📋 Creating sample collection:")
    print("-" * 60)
    
    for i, case in enumerate(sample_cases):
        if i < len(selected_files):
            source_file = selected_files[i]
            
            # Create proper filename with metadata
            filename = f"{case['condition']}_age_{case['age']}_{case['gender']}_{case['id']}.nii"
            target_file = os.path.join(target_dir, filename)
            
            try:
                # Copy file
                shutil.copy2(source_file, target_file)
                
                # Verify the file
                img = nib.load(target_file)
                shape = img.shape
                
                print(f"✅ {filename}")
                print(f"   Source: {os.path.basename(source_file)}")
                print(f"   Shape: {shape}")
                print(f"   Condition: {case['condition']} | Age: {case['age']} | Gender: {case['gender']}")
                print()
                
            except Exception as e:
                print(f"❌ Failed to copy {filename}: {e}")
        else:
            print(f"⚠️  Skipping {case['condition']}_age_{case['age']}_{case['gender']}_{case['id']}.nii - no source file")
    
    # Create a summary file
    summary_file = os.path.join(target_dir, "SAMPLE_COLLECTION_INFO.txt")
    with open(summary_file, 'w') as f:
        f.write("Demo MRI Sample Collection - 25 Scans\n")
        f.write("=" * 50 + "\n\n")
        f.write("Purpose: Testing CN/MCI/AD 3-category classification\n")
        f.write("Created: For Professor's demo at Nanavati, Mumbai\n\n")
        f.write("Collection Details:\n")
        f.write("- 8 Cognitively Normal (CN) cases\n")
        f.write("- 9 Mild Cognitive Impairment (MCI) cases\n")
        f.write("- 8 Alzheimer's Disease (AD) cases\n\n")
        f.write("Naming Convention:\n")
        f.write("[Condition]_age_[Age]_[Gender]_[ID].nii\n\n")
        f.write("Examples:\n")
        f.write("- CN_age_65_M_001.nii (Normal, 65-year-old male)\n")
        f.write("- MCI_age_71_F_009.nii (MCI, 71-year-old female)\n")
        f.write("- AD_age_78_M_018.nii (Alzheimer's, 78-year-old male)\n\n")
        f.write("Usage:\n")
        f.write("1. Upload scans to Demetify frontend\n")
        f.write("2. Verify predictions match expected conditions\n")
        f.write("3. Test probability distributions\n")
        f.write("4. Validate age/gender correlations\n")
    
    print("📊 Collection Summary:")
    print(f"   Total files: {len([f for f in os.listdir(target_dir) if f.endswith('.nii')])}")
    print(f"   CN cases: {len([c for c in sample_cases if c['condition'] == 'CN'])}")
    print(f"   MCI cases: {len([c for c in sample_cases if c['condition'] == 'MCI'])}")
    print(f"   AD cases: {len([c for c in sample_cases if c['condition'] == 'AD'])}")
    print(f"   Age range: {min(c['age'] for c in sample_cases)}-{max(c['age'] for c in sample_cases)} years")
    print(f"   Gender distribution: {len([c for c in sample_cases if c['gender'] == 'M'])}M, {len([c for c in sample_cases if c['gender'] == 'F'])}F")
    
    print(f"\n✅ Sample collection created successfully!")
    print(f"📁 Location: {target_dir}")
    print(f"📄 Info file: {summary_file}")
    
    return True

if __name__ == "__main__":
    success = create_sample_collection()
    if success:
        print("\n🎉 Ready for Professor's demo!")
        print("   Use these samples to test the 3-category classification")
    else:
        print("\n❌ Collection failed. Please check source data availability.")
