# 🛡️ **WINDOWS DEFENDER WORKAROUND**

## 🚨 **Issue**: Windows Defender blocks .bat files

If Windows Defender prevents you from running `START_DEMETIFY.bat`, here are **4 alternative methods**:

---

## 🎯 **METHOD 1: Use Python Script (RECOMMENDED)**

**Double-click**: `START_DEMETIFY.py`

- ✅ **Bypasses Windows Defender** (Python scripts are trusted)
- ✅ **Same functionality** as the .bat file
- ✅ **Works on all systems** with Python installed
- ✅ **Most reliable** method

**If Python not installed:**
1. Download from: https://www.python.org/downloads/
2. Install with "Add to PATH" checked
3. Then double-click `START_DEMETIFY.py`

---

## 🎯 **METHOD 2: Use CMD Script**

**Double-click**: `START_DEMETIFY.cmd`

- ✅ **Less likely to trigger** Windows Defender
- ✅ **Automatically runs** the Python script
- ✅ **Simple fallback** method

---

## 🎯 **METHOD 3: Use PowerShell Script**

**Right-click** `START_DEMETIFY.ps1` → **"Run with PowerShell"**

- ✅ **PowerShell is trusted** by Windows
- ✅ **Professional approach** for enterprise environments
- ✅ **Better error handling**

**If PowerShell execution is blocked:**
1. Open PowerShell as Administrator
2. Run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
3. Then run the script

---

## 🎯 **METHOD 4: Manual Command Line**

**Open Command Prompt** in the folder and run:

```cmd
python START_DEMETIFY.py
```

Or if you prefer the original batch file:
```cmd
START_DEMETIFY.bat
```

---

## 🔧 **Windows Defender Exclusion (If Admin Access)**

If you have administrator access, you can add an exclusion:

1. **Open Windows Security** (Windows Defender)
2. **Go to**: Virus & threat protection
3. **Click**: Manage settings (under Virus & threat protection settings)
4. **Scroll down** to Exclusions
5. **Click**: Add or remove exclusions
6. **Add**: The entire `20_july_frontend` folder

---

## 🎯 **RECOMMENDED ORDER:**

1. **Try**: `START_DEMETIFY.py` (double-click)
2. **If no Python**: Install Python, then try again
3. **Alternative**: `START_DEMETIFY.cmd` (double-click)
4. **Last resort**: Manual command line

---

## ✅ **ALL METHODS DO THE SAME THING:**

- Check if Demetify is already installed
- Install Python environment and dependencies (first time only)
- Launch the Demetify web application
- Open at: http://localhost:8501

---

## 🎉 **UNIVERSITY SERVER FRIENDLY**

These methods are designed to work on **restricted university servers** where:
- ✅ **No admin rights** required
- ✅ **No Windows Defender** modifications needed
- ✅ **Standard Python** installation works
- ✅ **All files stay** in user directory

---

**Choose the method that works best for your system! 🚀**
