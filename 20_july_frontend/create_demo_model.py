"""
Create a demo model file for testing the CN/MCI/AD frontend
"""

import torch
import torch.nn as nn
from cn_mci_ad_model import MemoryEfficientCNN

def create_demo_model():
    """Create a demo model with random weights for testing"""
    print("🔧 Creating demo model for testing...")
    
    # Create model
    model = MemoryEfficientCNN(num_classes=3)
    
    # Save model state dict
    torch.save(model.state_dict(), 'memory_efficient_cnn_model.pth')
    
    print("✅ Demo model created: memory_efficient_cnn_model.pth")
    print("   Note: This is a demo model with random weights")
    print("   For production, use the trained model from the cluster")

if __name__ == "__main__":
    create_demo_model()
