#!/usr/bin/env python3
"""
Simple Demetify Launcher - Standalone executable version
"""

import os
import sys
import subprocess
import webbrowser
import time
import tkinter as tk
from tkinter import messagebox
import threading
from pathlib import Path

def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class SimpleLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧠 Demetify - Alzheimer's Assessment")
        self.root.geometry("400x300")
        self.root.resizable(False, False)

        # Center the window
        self.root.eval('tk::PlaceWindow . center')

        self.setup_ui()
        self.process = None

    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = tk.Label(main_frame, text="🧠 Demetify",
                              font=("Arial", 20, "bold"), fg="#2E86AB")
        title_label.pack(pady=(0, 10))

        subtitle_label = tk.Label(main_frame, text="Alzheimer's Disease Assessment",
                                 font=("Arial", 12))
        subtitle_label.pack(pady=(0, 20))

        # Status
        self.status_label = tk.Label(main_frame, text="Ready to launch",
                                   font=("Arial", 10), fg="#666")
        self.status_label.pack(pady=(0, 20))

        # Launch button
        self.launch_btn = tk.Button(main_frame, text="🚀 Launch Demetify",
                                  command=self.launch_app,
                                  font=("Arial", 12, "bold"),
                                  bg="#2E86AB", fg="white",
                                  padx=20, pady=10)
        self.launch_btn.pack(pady=10)

        # Stop button
        self.stop_btn = tk.Button(main_frame, text="⏹ Stop",
                                command=self.stop_app,
                                font=("Arial", 10),
                                state=tk.DISABLED)
        self.stop_btn.pack(pady=5)

        # Browser button
        self.browser_btn = tk.Button(main_frame, text="🌐 Open Browser",
                                   command=self.open_browser,
                                   font=("Arial", 10),
                                   state=tk.DISABLED)
        self.browser_btn.pack(pady=5)

        # Info
        info_label = tk.Label(main_frame,
                            text="Professional AI-powered assessment using MRI scans\n"
                                 "Upload .nii/.nii.gz files for analysis",
                            font=("Arial", 9), fg="#666", justify=tk.CENTER)
        info_label.pack(pady=(20, 0))

    def update_status(self, message):
        self.status_label.config(text=message)
        self.root.update()

    def launch_app(self):
        def launch_thread():
            try:
                self.launch_btn.config(state=tk.DISABLED)
                self.update_status("Starting Demetify...")

                # Get the path to the Streamlit app
                app_path = resource_path("demetify_ncomms2022_app.py")

                if not os.path.exists(app_path):
                    messagebox.showerror("Error", f"Application file not found: {app_path}")
                    return

                # Launch Streamlit
                cmd = [sys.executable, "-m", "streamlit", "run", app_path,
                       "--server.port", "8501", "--server.address", "0.0.0.0",
                       "--server.headless", "true"]

                self.process = subprocess.Popen(cmd,
                                              stdout=subprocess.PIPE,
                                              stderr=subprocess.PIPE)

                # Wait for server to start
                time.sleep(5)

                self.update_status("Demetify running at http://localhost:8501")
                self.stop_btn.config(state=tk.NORMAL)
                self.browser_btn.config(state=tk.NORMAL)

                # Auto-open browser
                self.open_browser()

            except Exception as e:
                messagebox.showerror("Launch Error", f"Failed to start Demetify:\n{str(e)}")
            finally:
                self.launch_btn.config(state=tk.NORMAL)

        threading.Thread(target=launch_thread, daemon=True).start()

    def stop_app(self):
        try:
            if self.process:
                self.process.terminate()
                self.process = None

            self.update_status("Demetify stopped")
            self.stop_btn.config(state=tk.DISABLED)
            self.browser_btn.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Stop Error", f"Failed to stop Demetify:\n{str(e)}")

    def open_browser(self):
        try:
            webbrowser.open("http://localhost:8501")
        except Exception as e:
            messagebox.showerror("Browser Error", f"Failed to open browser:\n{str(e)}")

    def on_closing(self):
        if self.process:
            self.stop_app()
        self.root.destroy()

    def run(self):
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    launcher = SimpleLauncher()
    launcher.run()