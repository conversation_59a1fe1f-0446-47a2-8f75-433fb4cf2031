# Demetify PowerShell Launcher
# Alternative to batch file that may bypass Windows Defender

Write-Host "🧠 Demetify - AI Alzheimer's Assessment System" -ForegroundColor Cyan
Write-Host "University of Illinois at Urbana-Champaign" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host ""

# Try to run the Python script
try {
    Write-Host "Starting Demetify..." -ForegroundColor Green
    Write-Host ""
    
    # Check if Python is available
    $pythonCmd = $null
    if (Get-Command python -ErrorAction SilentlyContinue) {
        $pythonCmd = "python"
    } elseif (Get-Command py -ErrorAction SilentlyContinue) {
        $pythonCmd = "py"
    } else {
        Write-Host "Python not found. Please install Python from:" -ForegroundColor Red
        Write-Host "https://www.python.org/downloads/" -ForegroundColor Yellow
        Write-Host ""
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Run the Python launcher
    & $pythonCmd "START_DEMETIFY.py"
    
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}

Write-Host ""
Write-Host "Demetify session ended." -ForegroundColor Green
Read-Host "Press Enter to exit"
