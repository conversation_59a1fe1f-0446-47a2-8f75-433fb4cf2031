#!/usr/bin/env python3
"""
Demetify Portable Launcher
A Windows Defender-friendly launcher that sets up the environment and starts the application
"""

import os
import sys
import subprocess
import platform
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import webbrowser
from pathlib import Path

class DemetifyLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧠 Demetify - Alzheimer's Assessment")
        self.root.geometry("500x400")
        self.root.resizable(False, False)

        # Set icon if available
        try:
            self.root.iconbitmap("demetify.ico")
        except:
            pass

        self.setup_ui()
        self.python_path = None
        self.app_process = None

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="🧠 Demetify", font=("Arial", 24, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="Alzheimer's Disease Assessment System",
                                 font=("Arial", 12))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready to launch",
                                    font=("Arial", 10))
        self.status_label.grid(row=2, column=0, columnspan=2, pady=(0, 10))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, length=400, mode='indeterminate')
        self.progress.grid(row=3, column=0, columnspan=2, pady=(0, 20), sticky=(tk.W, tk.E))

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)

        # Launch button
        self.launch_btn = ttk.Button(button_frame, text="🚀 Launch Demetify",
                                   command=self.launch_app)
        self.launch_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Stop button
        self.stop_btn = ttk.Button(button_frame, text="⏹ Stop",
                                 command=self.stop_app, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Open browser button
        self.browser_btn = ttk.Button(button_frame, text="🌐 Open Browser",
                                    command=self.open_browser, state=tk.DISABLED)
        self.browser_btn.pack(side=tk.LEFT)

        # Info text
        info_text = tk.Text(main_frame, height=8, width=60, wrap=tk.WORD,
                           font=("Arial", 9), state=tk.DISABLED)
        info_text.grid(row=5, column=0, columnspan=2, pady=(20, 0), sticky=(tk.W, tk.E))

        info_content = """Welcome to Demetify - Professional AI-powered Alzheimer's Disease Assessment

Features:
• MRI Preprocessing - Automatic T1 scan processing
• AD/CN Classification - Real-time deep learning predictions
• SHAP Interpretability - Visual explanations in seconds
• Professional Interface - Radiologist-friendly orientations

Instructions:
1. Click 'Launch Demetify' to start the application
2. Wait for the web interface to load
3. Upload your T1-weighted MRI scan (.nii or .nii.gz)
4. View results and interpretability analysis

The application will open in your default web browser at http://localhost:8501"""

        info_text.config(state=tk.NORMAL)
        info_text.insert(tk.END, info_content.strip())
        info_text.config(state=tk.DISABLED)

    def update_status(self, message):
        """Update status label"""
        self.status_label.config(text=message)
        self.root.update()

    def check_python(self):
        """Check if Python is available"""
        try:
            # Try to find Python executable
            python_candidates = [
                sys.executable,
                "python",
                "python3",
                "py"
            ]

            for candidate in python_candidates:
                try:
                    result = subprocess.run([candidate, "--version"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.python_path = candidate
                        return True
                except:
                    continue

            return False
        except Exception as e:
            print(f"Error checking Python: {e}")
            return False

    def install_dependencies(self):
        """Install required dependencies"""
        try:
            self.update_status("Installing dependencies...")

            # Install requirements
            cmd = [self.python_path, "-m", "pip", "install", "-r", "requirements.txt", "--user"]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode != 0:
                raise Exception(f"Failed to install dependencies: {result.stderr}")

            return True
        except Exception as e:
            messagebox.showerror("Installation Error", f"Failed to install dependencies:\n{str(e)}")
            return False

    def launch_app(self):
        """Launch the Streamlit application"""
        def launch_thread():
            try:
                self.launch_btn.config(state=tk.DISABLED)
                self.progress.start()

                # Check Python
                self.update_status("Checking Python installation...")
                if not self.check_python():
                    messagebox.showerror("Python Not Found",
                                       "Python is not installed or not found in PATH.\n"
                                       "Please install Python 3.8+ and try again.")
                    return

                # Install dependencies if needed
                self.update_status("Checking dependencies...")
                if not self.install_dependencies():
                    return

                # Launch Streamlit
                self.update_status("Starting Demetify...")
                cmd = [self.python_path, "-m", "streamlit", "run", "demetify_ncomms2022_app.py",
                       "--server.port", "8501", "--server.address", "0.0.0.0"]

                self.app_process = subprocess.Popen(cmd,
                                                  stdout=subprocess.PIPE,
                                                  stderr=subprocess.PIPE,
                                                  creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == "Windows" else 0)

                # Wait a moment for the server to start
                time.sleep(5)

                self.update_status("Demetify is running at http://localhost:8501")
                self.stop_btn.config(state=tk.NORMAL)
                self.browser_btn.config(state=tk.NORMAL)

                # Auto-open browser
                self.open_browser()

            except Exception as e:
                messagebox.showerror("Launch Error", f"Failed to launch Demetify:\n{str(e)}")
            finally:
                self.progress.stop()
                self.launch_btn.config(state=tk.NORMAL)

        threading.Thread(target=launch_thread, daemon=True).start()

    def stop_app(self):
        """Stop the application"""
        try:
            if self.app_process:
                self.app_process.terminate()
                self.app_process = None

            self.update_status("Demetify stopped")
            self.stop_btn.config(state=tk.DISABLED)
            self.browser_btn.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Stop Error", f"Failed to stop Demetify:\n{str(e)}")

    def open_browser(self):
        """Open the web browser"""
        try:
            webbrowser.open("http://localhost:8501")
        except Exception as e:
            messagebox.showerror("Browser Error", f"Failed to open browser:\n{str(e)}")

    def on_closing(self):
        """Handle window closing"""
        if self.app_process:
            self.stop_app()
        self.root.destroy()

    def run(self):
        """Run the launcher"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    launcher = DemetifyLauncher()
    launcher.run()