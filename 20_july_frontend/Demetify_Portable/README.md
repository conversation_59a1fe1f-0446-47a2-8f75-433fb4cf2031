# 🧠 Demetify - Alzheimer's Disease Assessment System

**Professional AI-powered Alzheimer's Disease Assessment using Structural MRI Scans**

*University of Illinois at Urbana-Champaign | Project Lead: Prof<PERSON> <PERSON><PERSON> Se<PERSON>dri*

---

## 🚀 **Quick Start - Click & Run**

### **🎯 PORTABLE EXECUTABLE (RECOMMENDED)**
**✅ Ready-to-use! No Python or installation required!**
1. **Download**: Get the `Demetify_Portable` folder
2. **Copy**: Move folder to any Windows PC
3. **Run**: Double-click `Demetify.exe`
4. **Launch**: Click "Launch Demetify" in the GUI
5. **Use**: <PERSON><PERSON><PERSON> opens automatically - upload your MRI scans!

### **🔧 For Developers (Build from Source)**
1. **Build**: Run `python3 build_portable.py` (Linux/Mac) or `BUILD_PORTABLE.bat` (Windows)
2. **Distribute**: Copy the generated `Demetify_Portable` folder

### **🐍 Python Alternative (If you have Python)**
- **Simple**: Double-click `RUN_DEMETIFY.py`
- **GUI**: Double-click `demetify_launcher.py`
- **Traditional**: Use the original `.bat` files

---

## 📋 **System Requirements**

**For Portable Version:**
- **OS**: Windows 10+ (no Python needed!)
- **RAM**: 8GB minimum (16GB recommended)
- **Storage**: 5GB free space
- **Internet**: Only for first-time model downloads

**For Python Version:**
- **OS**: Any OS with Python 3.8+
- **Dependencies**: Auto-installed

---

## 🎯 **Features**

- ✅ **MRI Preprocessing** - Automatic T1 scan processing
- ✅ **AD/CN Classification** - Real-time deep learning predictions
- ✅ **SHAP Interpretability** - Visual explanations in seconds
- ✅ **Professional Interface** - Radiologist-friendly orientations
- ✅ **Portable Deployment** - Copy & run on any Windows PC

---

## 📊 **Usage**

1. **Upload** T1-weighted MRI scan (.nii or .nii.gz)
2. **View** original and preprocessed scans
3. **Get** AD/CN classification with confidence scores
4. **Examine** SHAP interpretability heatmaps
5. **Review** risk assessment and cognitive scores

---

## 🔧 **Troubleshooting**

**Windows Defender Issues:**
- Use `RUN_DEMETIFY.py` instead of .bat files
- Or build portable version with `BUILD_PORTABLE.bat`

**Runtime Issues:**
- Ensure all files stay in same directory
- Check port 8501 is available
- Dependencies install automatically

---

## 📞 **Support**

- **Model**: Nature Communications 2022 publication
- **Performance**: 85%+ accuracy validated against neurologists
- **Processing**: Complete analysis in <2 minutes per scan

*Demetify v2.0 - Portable & Windows Defender Friendly*
