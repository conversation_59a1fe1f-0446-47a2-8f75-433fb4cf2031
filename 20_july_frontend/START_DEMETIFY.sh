#!/bin/bash

# Demetify - One-Click Start (Linux/Mac)
# Checks installations, installs if needed, then starts the program

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Clear screen and show banner
clear
echo -e "${CYAN}"
echo "  ██████╗ ███████╗███╗   ███╗███████╗████████╗██╗███████╗██╗   ██╗"
echo "  ██╔══██╗██╔════╝████╗ ████║██╔════╝╚══██╔══╝██║██╔════╝╚██╗ ██╔╝"
echo "  ██║  ██║█████╗  ██╔████╔██║█████╗     ██║   ██║█████╗   ╚████╔╝ "
echo "  ██║  ██║██╔══╝  ██║╚██╔╝██║██╔══╝     ██║   ██║██╔══╝    ╚██╔╝  "
echo "  ██████╔╝███████╗██║ ╚═╝ ██║███████╗   ██║   ██║██║        ██║   "
echo "  ╚═════╝ ╚══════╝╚═╝     ╚═╝╚══════╝   ╚═╝   ╚═╝╚═╝        ╚═╝   "
echo ""
echo "  AI-Powered Alzheimer's Disease Assessment System"
echo "  University of Illinois at Urbana-Champaign"
echo "  Project Lead: Prof. <PERSON><PERSON>"
echo -e "${NC}"
echo "================================================================"
echo ""

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if already installed and working
if [[ -f "launch_demetify.sh" ]]; then
    print_status "Checking existing installation..."
    
    # Try to source conda and test
    if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
        if conda activate demetify &>/dev/null; then
            print_success "Demetify is already installed and ready!"
            echo ""
            conda deactivate &>/dev/null || true
            exec ./launch_demetify.sh
        fi
        conda deactivate &>/dev/null || true
    fi
fi

print_status "First-time setup required. Installing Demetify..."
echo "This will take 10-15 minutes depending on your internet connection."
echo ""
read -p "Press Enter to continue..."

# Check if conda is available
if command -v conda &> /dev/null; then
    print_success "Conda already available"
    source_conda
elif [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
    print_success "Found Miniconda in home directory"
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
else
    print_status "Installing Miniconda (Python environment)..."
    
    # Create temp directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download and install Miniconda
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    
    print_status "Downloading Miniconda installer..."
    if ! curl -L -O "$MINICONDA_URL"; then
        print_error "Failed to download Miniconda installer"
        print_status "Please check your internet connection and try again."
        exit 1
    fi
    
    INSTALLER_NAME=$(basename "$MINICONDA_URL")
    print_status "Installing Miniconda..."
    bash "$INSTALLER_NAME" -b -p "$HOME/miniconda3"
    
    # Initialize conda
    "$HOME/miniconda3/bin/conda" init bash
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
    
    # Clean up
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    print_success "Miniconda installed"
fi

# Source conda function
source_conda() {
    if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    elif command -v conda &> /dev/null; then
        eval "$(conda shell.bash hook)"
    else
        print_error "Could not find conda installation"
        exit 1
    fi
}

source_conda

print_status "Setting up Demetify environment..."

# Create environment
print_status "Creating Python environment..."
conda create -n demetify python=3.9 -y > /dev/null 2>&1

# Activate environment
conda activate demetify

# Install dependencies
print_status "Installing AI libraries (this may take several minutes)..."
echo "- Installing PyTorch..."
if ! conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y > /dev/null 2>&1; then
    print_warning "CUDA version failed, installing CPU version..."
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y > /dev/null 2>&1
fi

echo "- Installing other dependencies..."
if ! pip install -r requirements.txt > /dev/null 2>&1; then
    print_warning "Some dependencies failed to install, but system should still work"
fi

# Create launcher
print_status "Creating launcher..."
cat > launch_demetify.sh << 'EOF'
#!/bin/bash

echo "🧠 Starting Demetify..."
echo ""

# Source conda
if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
elif command -v conda &> /dev/null; then
    eval "$(conda shell.bash hook)"
else
    echo "❌ Conda not found. Please run START_DEMETIFY.sh again."
    exit 1
fi

# Activate environment
conda activate demetify

echo "✅ Environment activated"
echo "🚀 Launching Demetify frontend..."
echo "   Access at: http://localhost:8501"
echo "   Press Ctrl+C to stop"
echo ""

# Launch Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
EOF

chmod +x launch_demetify.sh

# Test installation
print_status "Testing installation..."
if python test_system.py > /dev/null 2>&1; then
    print_success "✅ Installation completed successfully!"
else
    print_warning "Installation completed with some issues, but should still work"
fi

echo ""

# Launch the program
echo "================================================================"
echo "🚀 LAUNCHING DEMETIFY"
echo "================================================================"
echo ""
echo "The system will start in a few seconds..."
echo "Once started, open your web browser to: http://localhost:8501"
echo ""
echo "To stop the system: Press Ctrl+C in the terminal window"
echo ""

# Launch
exec ./launch_demetify.sh
