<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">

  <schema id="org.gnome.desktop.wm.preferences"
          path="/org/gnome/desktop/wm/preferences/">
    <key name="mouse-button-modifier" type="s">
      <default><![CDATA['<Super>']]></default>
      <summary>Modifier to use for modified window click actions</summary>
      <description>
        Clicking a window while holding down this modifier key  will move the
        window (left click), resize the window  (middle click), or show the
        window menu (right click). The middle and right click operations may
        be swapped using the “resize-with-right-button” key. Modifier is
        expressed as <![CDATA[“<Alt>”]]> or <![CDATA[“<Super>”]]> for example.
    </description>
    </key>
    <key name="resize-with-right-button" type="b">
      <default>false</default>
      <summary>Whether to resize with the right button</summary>
      <description>
        Set this to true to resize with the right button and show a menu with
        the middle button while holding down the key given in
        “mouse-button-modifier”; set it to false to make it work
        the opposite way around.
       </description>
    </key>
    <key name="button-layout" type="s">
      <default>'appmenu:close'</default>
      <summary>Arrangement of buttons on the titlebar</summary>
      <description>
        Arrangement of buttons on the titlebar. The value should be a string,
        such as  “menu:minimize,maximize,spacer,close”; the colon separates
        the  left corner of the window from the right corner, and  the button
        names are comma-separated. Duplicate buttons are not allowed. Unknown
        button names are silently ignored so that buttons can be added in
        future metacity versions  without breaking older versions. A special
        spacer tag can be used to insert some space between
        two adjacent buttons.
      </description>
    </key>
    <key name="focus-mode" enum="org.gnome.desktop.GDesktopFocusMode">
      <default>'click'</default>
      <summary>Window focus mode</summary>
      <description>
        The window focus mode indicates how windows are activated.  It has
        three possible values; “click” means windows must  be clicked in order
        to focus them, “sloppy” means windows are focused when the mouse enters
        the window, and “mouse” means windows are focused when the mouse enters
        the window and  unfocused when the mouse leaves the window.
      </description>
    </key>
    <key name="focus-new-windows"
         enum="org.gnome.desktop.GDesktopFocusNewWindows">
      <default>'smart'</default>
      <summary>Control how new windows get focus</summary>
      <description>
        This option provides additional control over how newly created windows
        get focus.  It has two possible values; “smart” applies the user’s
        normal focus mode, and “strict” results in windows started from a
        terminal not being given focus.
      </description>
    </key>
    <key name="raise-on-click" type="b">
      <default>true</default>
      <summary>
        Whether windows should be raised when their client area is clicked
      </summary>
      <description>
	The default, true, indicates that a window will be raised
	whenever its client area or its frame is clicked.

	Setting this to false means that a window will not be raised
	if it is clicked on the client area.  To raise it, one can
	click anywhere in the window’s frame, or Super-click on any
	part of the window.  This mode is useful if one uses many overlapping
	windows.
      </description>
    </key>
    <key name="action-double-click-titlebar"
         enum="org.gnome.desktop.GDesktopTitlebarAction">
      <default>'toggle-maximize'</default>
      <summary>Action on title bar double-click</summary>
      <description>
        This option determines the effects of double-clicking on the title bar.

        Current valid options are “toggle-shade”, which will shade/unshade the
        window, “toggle-maximize” which will maximize/unmaximize the window,
        “toggle-maximize-horizontally” and “toggle-maximize-vertically”
        which will maximize/unmaximize the window in that direction only,
        “minimize” which will minimize the window, “shade” which will roll
        the window up, “menu” which will display the window menu,
        “lower” which will put the window behind all the others,
        and “none” which will not do anything.
      </description>
      <!-- For compatibility with GConf strings (Metacity 2.30) -->
      <aliases>
        <alias value="toggle_shade" target="toggle-shade"/>
        <alias value="toggle_maximize" target="toggle-maximize"/>
        <alias value="toggle_maximize_horizontally"
               target="toggle-maximize-horizontally"/>
        <alias value="toggle_maximize_vertically"
               target="toggle-maximize-vertically"/>
      </aliases>
    </key>
    <key name="action-middle-click-titlebar"
         enum="org.gnome.desktop.GDesktopTitlebarAction">
      <default>'none'</default>
      <summary>Action on title bar middle-click</summary>
      <description>
        This option determines the effects of middle-clicking on the title bar.

        Current valid options are “toggle-shade”, which will shade/unshade
        the window, “toggle-maximize” which will maximize/unmaximize the window,
        “toggle-maximize-horizontally” and “toggle-maximize-vertically”
        which will maximize/unmaximize the window in that direction only,
        “minimize” which will minimize the window, “shade” which will roll
        the window up, “menu” which will display the window menu,
        “lower” which will put the window behind all the others,
        and “none” which will not do anything.
      </description>
      <!-- For compatibility with GConf strings (Metacity 2.30) -->
      <aliases>
        <alias value="toggle_shade" target="toggle-shade"/>
        <alias value="toggle_maximize" target="toggle-maximize"/>
        <alias value="toggle_maximize_horizontally"
               target="toggle-maximize-horizontally"/>
        <alias value="toggle_maximize_vertically"
               target="toggle-maximize-vertically"/>
      </aliases>
    </key>
    <key name="action-right-click-titlebar"
         enum="org.gnome.desktop.GDesktopTitlebarAction">
      <default>'menu'</default>
      <summary>Action on title bar right-click</summary>
      <description>
        This option determines the effects of right-clicking on the title bar.

        Current valid options are “toggle-shade”, which will shade/unshade
        the window, “toggle-maximize” which will maximize/unmaximize
        the window, “toggle-maximize-horizontally” and
        “toggle-maximize-vertically” which will maximize/unmaximize
        the window in that direction only, “minimize” which will minimize
        the window, “shade” which will roll the window up,
        “menu” which will display the window menu, “lower” which will put
        the window behind all the others, and “none” which will not do anything.
      </description>
      <!-- For compatibility with GConf strings (Metacity 2.30) -->
      <aliases>
        <alias value="toggle_shade" target="toggle-shade"/>
        <alias value="toggle_maximize" target="toggle-maximize"/>
        <alias value="toggle_maximize_horizontally"
               target="toggle-maximize-horizontally"/>
        <alias value="toggle_maximize_vertically"
               target="toggle-maximize-vertically"/>
      </aliases>
    </key>
    <key name="auto-raise" type="b">
      <default>false</default>
      <summary>Automatically raises the focused window</summary>
      <description>
        If set to true, and the focus mode is either “sloppy” or “mouse”
        then the focused window will be automatically raised after a delay
        specified by the auto-raise-delay key. This is not related to clicking
        on a window to raise it, nor to entering a window during drag-and-drop.
      </description>
    </key>
    <key name="auto-raise-delay" type="i">
      <range min="0" max="10000"/>
      <default>500</default>
      <summary>Delay in milliseconds for the auto raise option</summary>
      <description>
        The time delay before raising a window if auto-raise is set to true.
        The delay is given in thousandths of a second.
      </description>
    </key>
    <key name="theme" type="s">
      <default>'Adwaita'</default>
      <summary>Current theme</summary>
      <description>
        The theme determines the appearance of window borders, titlebar,
        and so forth.

        DEPRECATED: This key is deprecated and ignored.
      </description>
    </key>
    <key name="titlebar-uses-system-font" type="b">
      <default>true</default>
      <summary>Use standard system font in window titles</summary>
      <description>
        If true, ignore the titlebar-font option, and use the standard
        application font for window titles.
      </description>
    </key>
    <key name="titlebar-font" type="s">
      <default>'Cantarell Bold 11'</default>
      <summary>Window title font</summary>
      <description>
        A font description string describing a font for window titlebars.
        The size from the description will only be used if the
        titlebar-font-size option is set to 0. Also, this option is disabled
        if the titlebar-uses-desktop-font option is set to true.
      </description>
    </key>
    <key name="num-workspaces" type="i">
      <default>4</default>
      <range min="1" max="36"/>
      <summary>Number of workspaces</summary>
      <description>
        Number of workspaces. Must be more than zero, and has a fixed maximum
        to prevent making the desktop unusable by accidentally asking
        for too many workspaces.
      </description>
    </key>
    <key name="audible-bell" type="b">
      <default>true</default>
      <summary>System Bell is Audible</summary>
      <description>
        Determines whether applications or the system can generate audible
        “beeps”; may be used in conjunction with “visual bell”
        to allow silent “beeps”.
      </description>
    </key>
    <key name="visual-bell" type="b">
      <default>false</default>
      <summary>Enable Visual Bell</summary>
      <description>
        Turns on a visual indication when an application or the system issues
        a “bell” or “beep”; useful for the hard-of-hearing and for use
        in noisy environments.
      </description>
    </key>
    <key name="visual-bell-type"
         enum="org.gnome.desktop.GDesktopVisualBellType">
      <default>'fullscreen-flash'</default>
      <summary>Visual Bell Type</summary>
      <description>
        Tells the WM how to implement the visual indication that the
        system bell or another application “bell” indicator has been rung.

        Currently there are two valid values, “fullscreen-flash”, which
        causes a fullscreen white-black flash, and “frame-flash” which
        causes the titlebar of the application which sent the bell signal
        to flash.

        If the application which sent the bell is unknown (as is usually
        the case for the default “system beep”), the currently focused
        window’s titlebar is flashed.
      </description>
      <!-- For compatibility with GConf strings (Metacity 2.30) -->
      <aliases>
        <alias value="fullscreen" target="fullscreen-flash"/>
        <alias value="frame_flash" target="frame-flash"/>
      </aliases>
    </key>
    <key name="disable-workarounds" type="b">
      <default>false</default>
      <summary>
        Disable misfeatures that are required by old or broken applications
      </summary>
      <description>
        Some applications disregard specifications in ways that result in
        window manager misfeatures. This option puts the WM in a
        rigorously correct mode, which gives a more consistent
        user interface, provided one does not need to run any
        misbehaving applications.
      </description>
    </key>
    <key name="workspace-names" type="as">
      <default>[]</default>
      <summary>The names of the workspaces</summary>
      <description>
        Defines the names that should be assigned to workspaces.
        If the list is too long for the current number of workspaces,
        names in excess will be ignored. If the list is too short, or
        includes empty names, missing values will be replaced with the
        default (“Workspace N”).
      </description>
    </key>
  </schema>

</schemalist>
