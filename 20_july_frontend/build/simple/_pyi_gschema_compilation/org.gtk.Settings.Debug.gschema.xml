<?xml version="1.0" encoding="UTF-8"?>
<schemalist>

  <schema id='org.gtk.Settings.Debug' path='/org/gtk/settings/debug/'>
    <key name='enable-inspector-keybinding' type='b'>
      <default>false</default>
      <summary>Enable inspector keybinding</summary>
      <description>
        If this setting is true, GTK+ lets the user open an interactive
        debugging window with a keybinding. The default shortcuts for
        the keybinding are Control-Shift-I and Control-Shift-D.
      </description>
    </key>
    <key name='inspector-warning' type='b'>
      <default>true</default>
      <summary>Inspector warning</summary>
      <description>
        If this setting is true, GTK+ shows a warning before letting
        the user use the interactive debugger.
      </description>
    </key>
  </schema>

</schemalist>

