<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.notifications" path="/org/gnome/desktop/notifications/">
    <key name="show-banners" type="b">
      <default>true</default>
      <summary>Show notification banners</summary>
      <description>
        Whether notification banners are visible for application notifications.
      </description>
    </key>

    <key name="show-in-lock-screen" type="b">
      <default>true</default>
      <summary>Show notifications in the lock screen</summary>
      <description>
        Whether notifications are shown in the lock screen or not.
      </description>
    </key>

    <!-- Temporary hack until GSettingsList lands -->
    <key name="application-children" type="as">
      <default>[]</default>
      <summary />
      <description />
    </key>
  </schema>

  <schema id="org.gnome.desktop.notifications.application">
    <key name="application-id" type="s">
      <default>''</default>
      <summary>Application ID</summary>
      <description>
        The application that this policy is for.
      </description>
    </key>

    <key name="enable" type="b">
      <default>true</default>
      <summary>Enable notifications</summary>
      <description>
        Whether notifications are globally enabled for this application.
      </description>
    </key>

    <key name="enable-sound-alerts" type="b">
      <default>true</default>
      <summary>Enable sound alerts</summary>
      <description>
        Whether notifications should be accompanied by sound alerts.
      </description>
    </key>

    <key name="show-banners" type="b">
      <default>true</default>
      <summary>Show notification banners</summary>
      <description>
        Whether notification banners for this application are shown or not. Does not
        affect clicking on message tray buttons.
      </description>
    </key>

    <key name="force-expanded" type="b">
      <default>false</default>
      <summary>Force automatic expanding of banners</summary>
      <description>
        Whether notifications from this application are expanded automatically when in banner mode.
      </description>
    </key>

    <key name="show-in-lock-screen" type="b">
      <default>true</default>
      <summary>Show in the lock screen</summary>
      <description>
        Whether notifications from this application are shown when the screen is locked.
      </description>
    </key>

    <key name="details-in-lock-screen" type="b">
      <default>false</default>
      <summary>Show details in the lock screen</summary>
      <description>
        Whether the summary and body of notifications from this application will be
        visible in the locked screen.
      </description>
    </key>

  </schema>
</schemalist>
