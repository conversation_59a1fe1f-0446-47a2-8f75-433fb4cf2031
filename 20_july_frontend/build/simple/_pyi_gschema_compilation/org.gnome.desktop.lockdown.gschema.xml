<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">
  <schema id="org.gnome.desktop.lockdown" path="/org/gnome/desktop/lockdown/">
    <key name="disable-command-line" type="b">
      <default>false</default>
      <summary>Disable command line</summary>
      <description>
        Prevent the user from accessing the terminal or specifying a command 
        line to be executed. For example, this would disable access to the 
        panel’s “Run Application” dialog.
      </description>
    </key>
    <key name="disable-save-to-disk" type="b">
      <default>false</default>
      <summary>Disable saving files to disk</summary>
      <description>
        Prevent the user from saving files to disk. For example, this would 
        disable access to all applications’ “Save as” dialogs.
      </description>
    </key>
    <key name="disable-printing" type="b">
      <default>false</default>
      <summary>Disable printing</summary>
      <description>
        Prevent the user from printing. For example, this would disable access 
        to all applications’ “Print” dialogs.
      </description>
    </key>
    <key name="disable-print-setup" type="b">
      <default>false</default>
      <summary>Disable print setup</summary>
      <description>
        Prevent the user from modifying print settings. For example, this would 
        disable access to all applications’ “Print Setup” dialogs.
      </description>
    </key>
    <key name="disable-user-switching" type="b">
      <default>false</default>
      <summary>Disable user switching</summary>
      <description>
        Prevent the user from switching to another account while his session 
        is active.
      </description>
    </key>
    <key name="disable-lock-screen" type="b">
      <default>false</default>
      <summary>Disable lock screen</summary>
      <description>
        Prevent the user to lock his screen.
      </description>
    </key>
    <key name="disable-application-handlers" type="b">
      <default>false</default>
      <summary>Disable URL and MIME type handlers</summary>
      <description>
        Prevent running any URL or MIME type handler applications.
      </description>
    </key>
    <key name="disable-log-out" type="b">
      <default>false</default>
      <summary>Disable log out</summary>
      <description>
        Prevent the user from logging out.
      </description>
    </key>
    <key name="user-administration-disabled" type="b">
      <default>false</default>
      <summary>Disable user administration</summary>
      <description>
        Stop the user from modifying user accounts. By default, we allow adding
        and removing users, as well as changing other users settings.
      </description>
    </key>
    <key name="mount-removable-storage-devices-as-read-only" type="b">
      <default>false</default>
      <summary>Mount removable storage devices as read-only</summary>
      <description>
        Prevent users from writing or modifying files on removable storage
        devices (i.e. flash disks, mobile phones, cameras).
      </description>
    </key>
    <key name="disable-show-password" type="b">
      <default>false</default>
      <summary>Disable password showing</summary>
      <description>
        Disable the "Show Password" menu item in password entries.
      </description>
    </key>
  </schema>
</schemalist>
