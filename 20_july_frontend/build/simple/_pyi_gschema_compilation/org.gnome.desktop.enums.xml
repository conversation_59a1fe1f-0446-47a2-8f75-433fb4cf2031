
<!-- This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. -->

<schemalist>
<enum id="org.gnome.desktop.GDesktopProxyMode">
    <value nick="none" value="0"/>
    <value nick="manual" value="1"/>
    <value nick="auto" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopToolbarStyle">
    <value nick="both" value="0"/>
    <value nick="both-horiz" value="1"/>
    <value nick="icons" value="2"/>
    <value nick="text" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopToolbarIconSize">
    <value nick="small" value="0"/>
    <value nick="large" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopBackgroundStyle">
    <value nick="none" value="0"/>
    <value nick="wallpaper" value="1"/>
    <value nick="centered" value="2"/>
    <value nick="scaled" value="3"/>
    <value nick="stretched" value="4"/>
    <value nick="zoom" value="5"/>
    <value nick="spanned" value="6"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopBackgroundShading">
    <value nick="solid" value="0"/>
    <value nick="vertical" value="1"/>
    <value nick="horizontal" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMouseDwellMode">
    <value nick="window" value="0"/>
    <value nick="gesture" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMouseDwellDirection">
    <value nick="left" value="0"/>
    <value nick="right" value="1"/>
    <value nick="up" value="2"/>
    <value nick="down" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopClockFormat">
    <value nick="24h" value="0"/>
    <value nick="12h" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopScreensaverMode">
    <value nick="blank-only" value="0"/>
    <value nick="random" value="1"/>
    <value nick="single" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMagnifierMouseTrackingMode">
    <value nick="none" value="0"/>
    <value nick="centered" value="1"/>
    <value nick="proportional" value="2"/>
    <value nick="push" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMagnifierFocusTrackingMode">
    <value nick="none" value="0"/>
    <value nick="centered" value="1"/>
    <value nick="proportional" value="2"/>
    <value nick="push" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMagnifierCaretTrackingMode">
    <value nick="none" value="0"/>
    <value nick="centered" value="1"/>
    <value nick="proportional" value="2"/>
    <value nick="push" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopMagnifierScreenPosition">
    <value nick="none" value="0"/>
    <value nick="full-screen" value="1"/>
    <value nick="top-half" value="2"/>
    <value nick="bottom-half" value="3"/>
    <value nick="left-half" value="4"/>
    <value nick="right-half" value="5"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopTitlebarAction">
    <value nick="toggle-shade" value="0"/>
    <value nick="toggle-maximize" value="1"/>
    <value nick="toggle-maximize-horizontally" value="2"/>
    <value nick="toggle-maximize-vertically" value="3"/>
    <value nick="minimize" value="4"/>
    <value nick="none" value="5"/>
    <value nick="lower" value="6"/>
    <value nick="menu" value="7"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopFocusMode">
    <value nick="click" value="0"/>
    <value nick="sloppy" value="1"/>
    <value nick="mouse" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopFocusNewWindows">
    <value nick="smart" value="0"/>
    <value nick="strict" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopVisualBellType">
    <value nick="fullscreen-flash" value="0"/>
    <value nick="frame-flash" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopLocationAccuracyLevel">
    <value nick="country" value="0"/>
    <value nick="city" value="1"/>
    <value nick="neighborhood" value="2"/>
    <value nick="street" value="3"/>
    <value nick="exact" value="4"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopTouchpadHandedness">
    <value nick="right" value="0"/>
    <value nick="left" value="1"/>
    <value nick="mouse" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopTouchpadClickMethod">
    <value nick="default" value="0"/>
    <value nick="none" value="1"/>
    <value nick="areas" value="2"/>
    <value nick="fingers" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopTouchpadTapButtonMap">
    <value nick="default" value="0"/>
    <value nick="lrm" value="1"/>
    <value nick="lmr" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopDeviceSendEvents">
    <value nick="enabled" value="0"/>
    <value nick="disabled" value="1"/>
    <value nick="disabled-on-external-mouse" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopTabletMapping">
    <value nick="absolute" value="0"/>
    <value nick="relative" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopPadButtonAction">
    <value nick="none" value="0"/>
    <value nick="help" value="1"/>
    <value nick="switch-monitor" value="2"/>
    <value nick="keybinding" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopStylusButtonAction">
    <value nick="default" value="0"/>
    <value nick="middle" value="1"/>
    <value nick="right" value="2"/>
    <value nick="back" value="3"/>
    <value nick="forward" value="4"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopPointerAccelProfile">
    <value nick="default" value="0"/>
    <value nick="flat" value="1"/>
    <value nick="adaptive" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopUsbProtection">
    <value nick="lockscreen" value="0"/>
    <value nick="always" value="1"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopFontAntialiasingMode">
    <value nick="none" value="0"/>
    <value nick="grayscale" value="1"/>
    <value nick="rgba" value="2"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopFontHinting">
    <value nick="none" value="0"/>
    <value nick="slight" value="1"/>
    <value nick="medium" value="2"/>
    <value nick="full" value="3"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopFontRgbaOrder">
    <value nick="rgba" value="0"/>
    <value nick="rgb" value="1"/>
    <value nick="bgr" value="2"/>
    <value nick="vrgb" value="3"/>
    <value nick="vbgr" value="4"/>
  </enum>
<enum id="org.gnome.desktop.GDesktopColorScheme">
    <value nick="default" value="0"/>
    <value nick="prefer-dark" value="1"/>
    <value nick="prefer-light" value="2"/>
  </enum>
</schemalist>

<!-- Generated data ends here -->

