<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright © 2002 Havoc Pennington
  Copyright © 2002 Jonathan <PERSON>
  Copyright © 2003, 2004 <PERSON>-Alvarez
  Copyright © 2005 <PERSON><PERSON><PERSON>
  Copyright © 2005 Tony <PERSON>
  Copyright © 2006 Guilherme de S. Pastore
  Copyright © 2009, 2010 Behdad Esfahbod
  Copyright © 2008, 2010 <PERSON>

  This program is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with this program.  If not, see <http://www.gnu.org/licenses/>.


  Modified and adapted from gnome-terminal for use with Tilix by <PERSON>
-->
<schemalist gettext-domain="Tilix">

   <!-- Theme variants -->
   <enum id='com.gexperts.Tilix.ThemeVariant'>
    <value nick='system' value='0'/>
    <value nick='light'  value='1'/>
    <value nick='dark'   value='2'/>
  </enum>

  <!-- Background Image Options -->
  <enum id='com.gexperts.Tilix.Background.Image.Mode'>
    <value nick='scale' value='0'/>
    <value nick='tile' value='1'/>
    <value nick='center' value='2'/>
    <value nick='stretch' value='3'/>
  </enum>

  <!-- Background Image Scaling Options, note these
       options mirror the cairo_filter_t options covered
       in the documentation at the link below. Also note that
       selecting higher quality algorithms will produce better
       results but at the expense of performance, particularly
       when re-sizing windows.
   -->
  <enum id='com.gexperts.Tilix.Background.Image.Scale'>
    <value nick='fast' value='0'/>
    <value nick='good' value='1'/>
    <value nick='best' value='2'/>
    <value nick='nearest' value='3'/>
    <value nick='bilinear' value='4'/>
  </enum>

  <enum id='com.gexperts.Tilix.Bell.Mode'>
    <value nick='none' value='0'/>
    <value nick='sound' value='1'/>
    <value nick='icon' value='2'/>
    <value nick='icon-sound' value='3'/>
  </enum>

  <!-- Second Instance Options -->
  <enum id='com.gexperts.Tilix.NewInstance.Mode'>
    <value nick='new-window' value ='0'/>
    <value nick='new-session' value ='1'/>
    <value nick='split-right' value ='2'/>
    <value nick='split-down' value ='3'/>
    <value nick='focus-window' value='4'/>
  </enum>

  <!-- Terminal Title Options -->
  <enum id='com.gexperts.Tilix.Terminal.Title'>
    <value nick='normal' value ='0'/>
    <value nick='small' value ='1'/>
    <value nick='none' value ='2'/>
  </enum>

  <!-- Cursor -->
  <enum id='com.gexperts.Tilix.Cursor.BlinkMode'>
    <value nick='system' value='0'/>
    <value nick='on' value='1'/>
    <value nick='off' value='2'/>
  </enum>
  <enum id='com.gexperts.Tilix.Cursor.Shape'>
    <value nick='block' value='0'/>
    <value nick='ibeam' value='1'/>
    <value nick='underline' value='2'/>
  </enum>

  <enum id='com.gexperts.Tilix.Text.BlinkMode'>
    <value nick='never' value='0'/>
    <value nick='focused' value='1'/>
    <value nick='unfocused' value='2'/>
    <value nick='always' value='3'/>
  </enum>

   <enum id='com.gexperts.Tilix.CJKWidth'>
    <value nick='narrow' value='1'/>
    <value nick='wide'   value='2'/>
  </enum>

  <enum id='com.gexperts.Tilix.EraseBinding'>
    <value nick='auto' value='0'/>
    <value nick='ascii-backspace' value='1'/>
    <value nick='ascii-delete' value='2'/>
    <value nick='delete-sequence' value='3'/>
    <value nick='tty' value='4'/>
  </enum>

  <enum id='com.gexperts.Tilix.ExitAction'>
    <value nick='close' value='0'/>
    <value nick='restart' value='1'/>
    <value nick='hold' value='2'/>
  </enum>

  <!-- Quadrant enum, used for badge position -->
  <enum id='com.gexperts.Tilix.Quadrant'>
    <value nick='northwest' value = '0'/>
    <value nick='northeast' value = '1'/>
    <value nick='southwest' value = '2'/>
    <value nick='southeast' value = '3'/>
  </enum>

  <!-- Alignment enum -->
  <enum id='com.gexperts.Tilix.Alignment'>
    <value nick='left' value = '0'/>
    <value nick='center' value = '1'/>
    <value nick='right' value = '2'/>
  </enum>

  <enum id='com.gexperts.Window.Style'>
    <value nick='normal' value='0'/>
    <value nick='disable-csd' value='1'/>
    <value nick='disable-csd-hide-toolbar' value='2'/>
    <value nick='borderless' value='3'/>
  </enum>

  <!-- Position enum, used for tab position -->
  <enum id='com.gexperts.Tilix.Position'>
    <value nick='left' value = '0'/>
    <value nick='right' value = '1'/>
    <value nick='top' value = '2'/>
    <value nick='bottom' value = '3'/>
  </enum>

  <!-- Position enum, used for quake position -->
  <enum id='com.gexperts.Tilix.QuakePosition'>
    <value nick='top' value = '0'/>
    <value nick='bottom' value = '1'/>
  </enum>

  <!-- Global settings -->
  <schema id="com.gexperts.Tilix.Settings" path="/com/gexperts/Tilix/">

    <key name="window-state" type="i">
      <default>0</default>
      <summary>The window state (maximized, minimized, etc) to restore</summary>
      <description>Saves the last window state on exit and restores it upon new invocation</description>
    </key>
    <key name="window-save-state" type="b">
      <default>false</default>
      <summary>If true, the window state is saved and restored between invocations</summary>
      <description>If true, saves the last window state on exit and restores it upon new invocation</description>
    </key>
    <key name="window-style" enum="com.gexperts.Window.Style">
      <default>'normal'</default>
      <summary>Style to use for displaying the tilix window</summary>
      <description>The style to use for displaying the tilix window, enables disabling Client-Side_Decorations.</description>
    </key>
    <key name="app-title" type="s">
      <default>'${appName}: ${sessionName}'</default>
      <summary>The application title that is used</summary>
      <description>The title to use for the application, can be composed of literal strings and supported tokens.</description>
    </key>
    <key name="auto-hide-mouse" type="b">
      <default>false</default>
      <summary>Whether to automatically hide the mouse pointer when typing</summary>
      <description>If true, the mouse pointer is hidden when the user starts typing and shown again when interaction with the mouse occurs.</description>
    </key>
    <key name="prompt-on-new-session" type="b">
      <default>false</default>
      <summary>Whether to prompt for session settings or use defaults</summary>
      <description>If true, when creating a new session a prompt will appear to pick the session name and profile used.</description>
    </key>
    <key name="prompt-on-close" type="b">
      <default>true</default>
      <summary>Whether to prompt on close when multiple sessions are active</summary>
      <description>If true, a prompt is shown confirming closing the window when multiple sessions are active.</description>
    </key>
    <key name="prompt-on-close-process" type="b">
      <default>true</default>
      <summary>Whether to prompt on close when processes are still running</summary>
      <description>If true, a prompt is shown confirming closing the window when processes are running.</description>
    </key>
    <key name="prompt-on-delete-profile" type="b">
      <default>true</default>
      <summary>Whether to prompt on profile deletion</summary>
      <description>If true, a prompt is shown confirming the deletion of a profile.</description>
    </key>
    <key name="session-name" type="s">
      <default>'${title}'</default>
      <summary>The default name to use for a session</summary>
      <description>The default name of the session, terminal variables can be used in which case the active terminal resolves the variables.</description>
    </key>
    <key name="enable-transparency" type="b">
      <default>true</default>
      <summary>Whether transparency is enabled</summary>
      <description>If true, transparency is enabled in terminals.</description>
    </key>
    <key name="terminal-title-style" enum="com.gexperts.Tilix.Terminal.Title">
      <default>'normal'</default>
      <summary>The style to use for terminal titles</summary>
      <description>Which style to use for terminal titles, normal, small or none.</description>
    </key>
    <key name="terminal-title-show-when-single" type="b">
      <default>true</default>
      <summary>Show the terminal title even if its the only terminal</summary>
      <description>When enabled, the terminal title bar will remain visible when only a single terminal exists in the session</description>
    </key>
    <key name="enable-wide-handle" type="b">
      <default>false</default>
      <summary>Whether splitters use a narrow (default) or wide handle</summary>
      <description>If true, splitters between terminals use a wide handle. Only supported in GTK 2.16 and later.</description>
    </key>
    <key name="close-with-last-session" type="b">
      <default>true</default>
      <summary>Whether the window should close when the last session is closed</summary>
      <description>If false, when the last session is closed, a new session is opened.</description>
    </key>
    <key name="use-overlay-scrollbar" type="b">
      <default>true</default>
      <summary>If true, uses an overlay scrollbar instead of a separate widget</summary>
      <description>If true, uses an overlay scrollbar instead of a separate widget if GTK is 3.22 or greater.</description>
    </key>
    <!-- Dark Theme -->
    <key name="theme-variant" enum="com.gexperts.Tilix.ThemeVariant">
      <default>'system'</default>
      <summary>Which theme variant to use</summary>
    </key>
    <!-- Second Instance Options -->
    <key name="new-instance-mode" enum="com.gexperts.Tilix.NewInstance.Mode">
        <default>'new-window'</default>
        <summary>Default behavior when opening a second instance of Tilix</summary>
    </key>
    <!-- Notification Options -->
    <key name="notify-on-process-complete" type="b">
      <default>true</default>
      <summary>Whether to notify when a process is completed</summary>
      <description>If true, notifications will be sent when non-visible processes complete in terminals.</description>
    </key>
    <!-- Menubar Accelerator -->
    <key name="menu-accelerator-enabled" type="b">
      <default>false</default>
      <summary>Whether the standard GTK shortcut for menubar access is enabled</summary>
      <description>Normally you can access the menubar with F10. This can also be customized via gtkrc (gtk-menu-bar-accel = "whatever"). This option allows the standard menubar accelerator to be disabled.</description>
    </key>
    <!-- Middle-Click Close -->
    <key name="middle-click-close" type="b">
      <default>false</default>
      <summary>Whether clicking the middle mouse button closes the terminal</summary>
      <description>When true, clicking the middle mouse button on the terminal title bar will close it.</description>
    </key>
    <!-- Scroll with mousewheel and control -->
    <key name="control-scroll-zoom" type="b">
      <default>false</default>
      <summary>Whether the control key and the mouse wheel zooms the terminal</summary>
      <description>When true, using the control key with the mouse scroll wheel will zoom the terminal in and out.</description>
    </key>
    <!-- Accelerators Enabled-->
    <key name="accelerators-enabled" type="b">
      <default>true</default>
      <summary>Whether accelerators for shortcuts are enabled</summary>
      <description>When this is false, accelerators are disabled and any accelerators set will be ignored.</description>
    </key>
    <!-- Unsafe Paste, cribbed from Pantheon -->
    <key name="unsafe-paste-alert" type="b">
      <default>true</default>
      <summary>Alert the user about unsafe paste</summary>
      <description>Warn the user when pasting a command into the terminal that might be considered unsafe.</description>
    </key>
    <key name="paste-strip-first-char" type="b">
      <default>false</default>
      <summary>Strip the first character from a paste when comment or variable declaration</summary>
      <description>If true, if the first character is a comment (#) or a variable declaration($) it will be stripped on paste.</description>
    </key>
      <key name="paste-strip-trailing-whitespace" type="b">
      <default>false</default>
      <summary>Strip trailing whitespace on paste.</summary>
      <description>If true, trailing whitespace will be stripped when pasting.</description>
    </key>
    <key name="paste-advanced-default" type="b">
      <default>false</default>
      <summary>Use the advanced paste as the default paste</summary>
      <description>If true, the advanced paste dialog is always used when paste is activated.</description>
    </key>
    <key name="copy-on-select" type="b">
      <default>false</default>
      <summary>Automatically copy selected text to clipboard</summary>
      <description>If true, when text is selected in the terminal it is automatically copied to the clipboard.</description>
    </key>
    <!-- Search Options -->
    <key name="search-default-match-case" type="b">
      <default>false</default>
      <summary>Whether to match case</summary>
      <description>If true, searches will be case sensitive.</description>
    </key>
    <key name="search-default-match-entire-word" type="b">
      <default>false</default>
      <summary>Whether to match the entire word</summary>
      <description>If true, search must be the entire word in order to be considered a match.</description>
    </key>
    <key name="search-default-match-as-regex" type="b">
      <default>false</default>
      <summary>Whether to match using regular expressions</summary>
      <description>If true, the search will be done treating the search text as a regular expression.</description>
    </key>
    <key name="search-default-wrap-around" type="b">
      <default>true</default>
      <summary>Whether searches wrap the terminal buffer</summary>
      <description>If true, a search will move to the bottom of the terminal buffer when it reaches the top item and vice versa.</description>
    </key>
    <key name="encodings" type="as">
      <!-- Translators: Please note that this has to be a list of
           valid encodings (which are to be taken from the list in src/encoding.c).
           It has to include UTF-8 the list entries themselves are not to be
           translated. This is provided for customization of the default encoding
           menu; see bug 144810 for an use case. In most cases, this should be
           left alone.
      -->
      <default>["UTF-8"]</default>
      <summary>List of available encodings</summary>
      <description>
        A subset of possible encodings are presented in
        the Encoding submenu. This is a list of encodings
        to appear there.
      </description>
    </key>
    <key name="warn-vte-config-issue" type="b">
      <default>true</default>
      <summary>Whether to warn if the VTE configuration issue is detected</summary>
      <description>If true, a dialog will be displayed warning the user that VTE is not configured correctly.</description>
    </key>
    <key name="focus-follow-mouse" type="b">
      <default>false</default>
      <summary>Whether the focus follows the mouse</summary>
      <description>If true, a terminal will automatically be focused when the mouse moves over it.</description>
    </key>
    <!-- Background Image -->
    <key name="background-image" type="s">
      <default>''</default>
      <summary>Path of the background image</summary>
      <description>The path of the image to use for the background, requires transparency support.</description>
    </key>
    <key name="background-image-mode" enum="com.gexperts.Tilix.Background.Image.Mode">
      <default>'scale'</default>
      <summary>The mode to use when rendering the background image</summary>
      <description>Whether the background image is rendered scaled, tiled or centered in Tilix.</description>
    </key>
    <key name="background-image-scale" enum="com.gexperts.Tilix.Background.Image.Scale">
      <default>'bilinear'</default>
      <summary>The algorithm to use when scaling the background image</summary>
      <description>The cairo filter algorithm used to scale the background image, performance will deteroriate with higher quality.</description>
    </key>
    <key name="password-include-return" type="b">
      <default>true</default>
      <summary>Whether to include return character with password</summary>
      <description>If true, when selecting a password to submit to the terminal a return character will automatically be added.</description>
    </key>
    <key name="bookmark-include-return" type="b">
      <default>true</default>
      <summary>Whether to include return character with bookmark</summary>
      <description>If true, when selecting a bookmark to submit to the terminal a return character will automatically be added.</description>
    </key>
    <key name="sidebar-on-right" type="b">
      <default>false</default>
      <summary>Whether the sidebar should be on the right</summary>
      <description>If true, the sidebar will be attached to the right side of the window rather than the left.</description>
    </key>
    <!-- Quake Options -->
    <key name="quake-height-percent" type="i">
      <range min="10" max="100" />
      <default>40</default>
      <summary>The height of the terminal as a percent of the screen height</summary>
      <description>When in quake mode, the height of the terminal as a percentage of the screen height.</description>
    </key>
    <key name="quake-width-percent" type="i">
      <range min="10" max="100" />
      <default>100</default>
      <summary>The width of the terminal as a percent of the screen width</summary>
      <description>When in quake mode, the width of the terminal as a percentage of the screen width.</description>
    </key>
    <key name="quake-active-monitor" type="b">
      <default>true</default>
      <summary>Display the terminal on the active monitor</summary>
      <description>When in quake mode, always display the terminal on the active monitor.</description>
    </key>
    <key name="quake-specific-monitor" type="i">
      <default>0</default>
      <summary>Display the terminal on a specific monitor if available</summary>
      <description>When in quake mode, display the terminal on the specified monitor.</description>
    </key>
    <key name="quake-show-on-all-workspaces" type="b">
      <default>true</default>
      <summary>Display the terminal on all workspaces</summary>
      <description>When in quake mode, display the terminal on all workspaces.</description>
    </key>
    <!--
    <key name="quake-disable-animation" type="b">
      <default>false</default>
      <summary>Use a hint to request animation be disabled</summary>
      <description>When true, the quake window will have a hint to request that animation be disabled by the window manager.</description>
    </key>
    -->
    <key name="quake-hide-lose-focus" type="b">
      <default>false</default>
      <summary>Hide the quake window when focus is lost</summary>
      <description>When true, the quake window will be hidden automatically when focus is lost.</description>
    </key>
    <key name="quake-hide-lose-focus-delay" type="i">
      <default>150</default>
      <summary>Delay used to hide the quake window when focus is lost</summary>
      <description>Delay used to hide the quake window so that quick changes in focus do not hide the window.</description>
    </key>
    <key name="quake-alignment" enum="com.gexperts.Tilix.Alignment">
      <default>'center'</default>
      <summary>The alignment of the quake window</summary>
      <description>The alignment of the quake window when the width is less then 100%.</description>
    </key>
    <key name="quake-hide-headerbar" type="b">
      <default>false</default>
      <summary>Hide the headerbar when running in quake mode</summary>
      <description>When true, the headerbar of the quake window will be hidden.</description>
    </key>
    <key name="quake-tab-position" enum="com.gexperts.Tilix.Position">
      <default>'top'</default>
      <summary>The position where the tabs are displayed in Quake mode</summary>
      <description>Determines where the tabs are displayed relative to the notebook in quake mode.</description>
    </key>
    <key name="quake-keep-on-top" type="b">
      <default>true</default>
      <summary>When true the quake window is always kept on top</summary>
      <description>When true, the quake window will be kept on top of all other windows.</description>
    </key>
    <key name="quake-window-position" enum="com.gexperts.Tilix.QuakePosition">
      <default>'top'</default>
      <summary>The position where the window is displayed in Quake mode</summary>
      <description>Determines where the window is displayed relative to the screen in quake mode.</description>
    </key>

    <!-- Advanced Paste Options -->
    <key name="advanced-paste-replace-tabs" type="b">
      <default>false</default>
      <summary>Replace tabs with spaces</summary>
      <description>If true, replaces tabs with spaces in advanced paste.</description>
    </key>
    <key name="advanced-paste-space-count" type="i">
      <default>4</default>
      <summary>The number of spaces to use when replacing tabs</summary>
      <description>When using advanced paste, the number of spaces to use when replacing tabs.</description>
    </key>
    <key name="advanced-paste-replace-crlf" type="b">
      <default>false</default>
      <summary>Replace CRLF with LF</summary>
      <description>If true, replaces CRLF sequences with LF in advanced paste.</description>
    </key>
    <key name="recent-session-files" type="as">
      <default>[]</default>
      <summary>List of recent session files used</summary>
      <description>A list of recently used session files, used for quick loading.</description>
    </key>

    <!-- Setting to include proxy if set in envonment variables-->
    <key name="set-proxy-env" type="b">
      <default>true</default>
      <summary>Set proxy environment variables</summary>
      <description>If true, sets the proxy environment variables if configured in Gnome.</description>
    </key>

    <!-- Control-click titlebar -->
    <key name="control-click-titlebar" type="b">
      <default>false</default>
      <summary>Whether to require control modifier to edit title on click</summary>
      <description>If true, editing the title requires using the control key modifier when clicking.</description>
    </key>

    <!-- Enable process monitor -->
    <key name="process-monitor" type="b">
      <default>false</default>
      <summary>Whether to to enable the process monitor</summary>
      <description>If true, the process monitor will be enabled and the option to add the process name to the title will be available.</description>
    </key>

    <!-- Global settings for triggers and custom links-->
    <key name="custom-hyperlinks" type="as">
      <default>[]</default>
      <summary>A list of custom links that can be clicked on in the terminal</summary>
      <description>A list of custom links, each link consists of a regex and optional launcher encoded according to CSV rules.</description>
    </key>
    <key name="triggers" type="as">
      <default>[]</default>
      <summary>A list of definitions that trigger actions based on content matches</summary>
      <description>A list of trigger definitions with each definition consisting of a regex, action to trigger and parameter.</description>
    </key>
    <key name="triggers-lines" type="i">
      <default>256</default>
      <summary>The maximum number of lines to check for trigger matches</summary>
      <description>When the terminal reports a block of text, this setting determines how many lines are checked for trigger matches starting with the last line.</description>
    </key>
    <key name="trigger-unlimit-lines" type="b">
      <default>false</default>
      <summary>Whether an unlimited number of lines should be processed for triggers</summary>
      <description>If true, all available lines will be processed for triggers. When outputing large amounts of text to the terminal this may impact performance.</description>
    </key>

    <key name="use-tabs" type="b">
      <default>false</default>
      <summary>When true tabs are shown instead of the sidebar</summary>
      <description>If true, notebook tabs are used instead of the sidebar as the UI metaphor for sessions.</description>
    </key>
    <key name="tab-position" enum="com.gexperts.Tilix.Position">
      <default>'top'</default>
      <summary>The position where the tabs are displayed</summary>
      <description>Determines where the tabs are displayed relative to the notebook.</description>
    </key>
  </schema>

  <!-- SettingsList base schema -->

  <schema id="com.gexperts.Tilix.SettingsList">
    <key name="list" type="as">
      <default>[]</default>
    </key>
    <key name="default" type="s">
      <default>''</default>
    </key>
  </schema>

  <!-- Profiles list schema -->

  <schema id="com.gexperts.Tilix.ProfilesList"
          extends="com.gexperts.Tilix.SettingsList"
          path="/com/gexperts/Tilix/profiles/">
    <override name="list">['2b7c4080-0ddd-46c5-8f23-563fd3ba789d']</override>
    <override name="default">'2b7c4080-0ddd-46c5-8f23-563fd3ba789d'</override>
  </schema>

  <!-- A terminal profile -->

  <schema id="com.gexperts.Tilix.Profile">
    <key name="visible-name" type="s">
      <default l10n="messages" context="visible-name">'Unnamed'</default>
      <summary>Human-readable name of the profile</summary>
      <description>Human-readable name of the profile.</description>
    </key>
    <key name="shortcut" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to switch to profile</summary>
    </key>
    <key name="default-size-columns" type="i">
      <range min="16" max="511" />
      <default>80</default>
      <summary>Default number of columns</summary>
      <description>Number of columns in newly created terminal windows. Has no effect if use_custom_default_size is not enabled.</description>
    </key>
    <key name="default-size-rows" type="i">
      <range min="4" max="511" />
      <default>24</default>
      <summary>Default number of rows</summary>
      <description>Number of rows in newly created terminal windows. Has no effect if use_custom_default_size is not enabled.</description>
    </key>
    <key name="cell-height-scale" type="d">
      <range min="1.0" max="2.0" />
      <default>1.0</default>
      <summary>Scale factor for the cell height to increase line spacing. (Does not increase the font’s height.)</summary>
    </key>
    <key name="cell-width-scale" type="d">
      <range min="1.0" max="2.0" />
      <default>1.0</default>
      <summary>Scale factor for the cell width to increase letter spacing. (Does not increase the font’s width.)</summary>
    </key>
    <key name="cursor-blink-mode" enum="com.gexperts.Tilix.Cursor.BlinkMode">
      <default>'system'</default>
      <summary>Whether to blink the cursor</summary>
      <description>The possible values are "system" to use the global cursor blinking settings, or "on" or "off" to set the mode explicitly.</description>
    </key>
    <key name="cursor-shape" enum="com.gexperts.Tilix.Cursor.Shape">
      <default>'block'</default>
      <summary>The cursor appearance</summary>
    </key>
    <key name="text-blink-mode" enum="com.gexperts.Tilix.Text.BlinkMode">
      <default>'always'</default>
      <summary>Whether to enable blinking text</summary>
      <description>The possible values are "never" to disable text blinking, "always" to enable, or "focused" or "unfocused" to set the mode explicitly.</description>
    </key>
    <key name="terminal-bell" enum="com.gexperts.Tilix.Bell.Mode">
      <default>'sound'</default>
      <summary>What to do when a bell signal is emitted in a terminal</summary>
      <description>Determines whether to do nothing, play a sound, show an icon or do both when a bell signal is emitted.</description>
    </key>
    <key name="allow-bold" type="b">
      <default>true</default>
      <summary>Whether to allow bold text</summary>
      <description>If true, allow applications in the terminal to make text boldface.</description>
    </key>
    <key name="bold-is-bright" type="b">
      <default>true</default>
      <summary>Whether bold is also bright</summary>
      <description>If true, setting bold on the first 8 colors also switches to their bright variants.</description>
    </key>
    <key name="rewrap-on-resize" type="b">
      <default>true</default>
      <summary>Whether to rewrap the terminal contents on window resize</summary>
    </key>
    <!-- Font -->
    <key name="use-system-font" type="b">
      <default>true</default>
      <summary>Whether to use the system monospace font</summary>
    </key>
    <key name="font" type="s">
      <default>'Monospace 12'</default>
      <summary>A Pango font name and size</summary>
    </key>
    <key name="draw-margin" type="i">
      <default>80</default>
      <summary>Draws a margin line at the column specified</summary>
      <description>Draws a margin line at the column specified, 0 indicates no margin.</description>
    </key>
    <!--Transparency-->
    <key name="background-transparency-percent" type="i">
      <range min="0" max="100" />
      <default>0</default>
      <summary>The amount of transparency to apply to the background</summary>
    </key>
    <!--Dim-->
    <key name="dim-transparency-percent" type="i">
      <range min="0" max="100" />
      <default>0</default>
      <summary>The amount of transparency to apply to the unfocused dim effect</summary>
    </key>
    <!-- Colors -->
    <key name="foreground-color" type="s">
      <default>'#00FF00'</default>
      <summary>Default color of text in the terminal</summary>
      <description>Default color of text in the terminal, as a color specification (can be HTML-style hex digits, or a color name such as "red").</description>
    </key>
    <key name="background-color" type="s">
      <default>'#000000'</default>
      <summary>Default color of terminal background</summary>
      <description>Default color of terminal background, as a color specification (can be HTML-style hex digits, or a color name such as "red").</description>
    </key>
    <key name="cursor-colors-set" type="b">
      <default>false</default>
      <summary>Whether to use custom cursor colors</summary>
      <description>If true, use the cursor colors from the profile.</description>
    </key>
    <key name="cursor-background-color" type="s">
      <default>'#000000'</default>
      <summary>Cursor background color</summary>
      <description>Custom color of the background of the terminal's cursor, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if cursor-colors-set is false.</description>
    </key>
    <key name="cursor-foreground-color" type="s">
      <default>'#ffffff'</default>
      <summary>Cursor foreground colour</summary>
      <description>Custom color for the foreground of the text character at the terminal's cursor position, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if cursor-colors-set is false.</description>
    </key>
    <key name="highlight-colors-set" type="b">
      <default>false</default>
      <summary>Whether to use custom highlight colors</summary>
      <description>If true, use the highlight colors from the profile.</description>
    </key>
    <key name="highlight-background-color" type="s">
      <default>'#000000'</default>
      <summary>Highlight background color</summary>
      <description>Custom color of the background of the terminal's highlight, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if highlight-colors-set is false.</description>
    </key>
    <key name="highlight-foreground-color" type="s">
      <default>'#ffffff'</default>
      <summary>Highlight foreground colour</summary>
      <description>Custom color for the foreground of the text character at the terminal's highlight position, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if highlight-colors-set is false.</description>
    </key>
    <key name="palette" type="as">
      <default>["#2E2E34343636",
                "#CCCC00000000",
                "#4E4E9A9A0606",
                "#C4C4A0A00000",
                "#34346565A4A4",
                "#757550507B7B",
                "#060698209A9A",
                "#D3D3D7D7CFCF",
                "#555557575353",
                "#EFEF29292929",
                "#8A8AE2E23434",
                "#FCFCE9E94F4F",
                "#72729F9FCFCF",
                "#ADAD7F7FA8A8",
                "#3434E2E2E2E2",
                "#EEEEEEEEECEC"]</default>
      <summary>Palette for terminal applications</summary>
    </key>
    <key name="use-theme-colors" type="b">
      <default>true</default>
      <summary>Whether to use the colors from the theme for the terminal widget</summary>
    </key>
    <!-- Scroll Options -->
    <key name="show-scrollbar" type="b">
      <default>true</default>
      <summary>When to show the scrollbar</summary>
    </key>
    <key name="scrollback-lines" type="i">
      <default>8192</default>
      <summary>Number of lines to keep in scrollback</summary>
      <description>Number of scrollback lines to keep around. You can scroll back in the terminal by this number of lines; lines that don't fit in the scrollback are discarded. If scrollback_unlimited is true, this value is ignored.</description>
    </key>
    <key name="scrollback-unlimited" type="b">
      <default>false</default>
      <summary>Whether an unlimited number of lines should be kept in scrollback</summary>
      <description>If true, scrollback lines will never be discarded. The scrollback history is stored on disk temporarily, so this may cause the system to run out of disk space if there is a lot of output to the terminal.</description>
    </key>
    <key name="scroll-on-keystroke" type="b">
      <default>true</default>
      <summary>Whether to scroll to the bottom when a key is pressed</summary>
      <description>If true, pressing a key jumps the scrollbar to the bottom.</description>
    </key>
    <key name="scroll-on-output" type="b">
      <default>false</default>
      <summary>Whether to scroll to the bottom when there's new output</summary>
      <description>If true, whenever there's new output the terminal will scroll to the bottom.</description>
    </key>
    <!-- Compatibility -->
    <key name="backspace-binding" enum="com.gexperts.Tilix.EraseBinding">
      <default>'ascii-delete'</default>
      <summary>The code sequence the Backspace key generates</summary>
    </key>
    <key name="delete-binding" enum="com.gexperts.Tilix.EraseBinding">
      <default>'delete-sequence'</default>
      <summary>The code sequence the Delete key generates</summary>
    </key>
    <key name="cjk-utf8-ambiguous-width" enum="com.gexperts.Tilix.CJKWidth">
      <default>'narrow'</default>
      <summary>Whether ambiguous-width characters are narrow or wide when using UTF-8 encoding</summary>
    </key>
    <!-- Terminal Title -->
    <key name="terminal-title" type="s">
      <default>'${id}: ${title}'</default>
      <summary>The string to display as the terminal title</summary>
      <description>The title supports tokens which are replaced at runtime, additionally pango markup can be used as well.</description>
    </key>
    <key name="select-by-word-chars" type="s">
      <default>'-,./?%&amp;#:_'</default>
      <summary>Characters which are considered as part of word-wise selection</summary>
      <description>Set of characters which will be considered parts of a word when doing word-wise selection, in addition to the default which only considers alphanumeric characters part of a word.</description>
    </key>
    <!-- Command Tab Options -->
    <key name="exit-action" enum="com.gexperts.Tilix.ExitAction">
      <default>'close'</default>
      <summary>What to do with the terminal when the child command exits</summary>
      <description>Possible values are "close" to close the terminal, "restart" to restart the command, and "hold" to keep the terminal open with no command running inside.</description>
    </key>
    <key name="login-shell" type="b">
      <default>false</default>
      <summary>Whether to launch the command in the terminal as a login shell</summary>
      <description>If true, the command inside the terminal will be launched as a login shell (argv[0] will have a hyphen in front of it).</description>
    </key>
    <key name="use-custom-command" type="b">
      <default>false</default>
      <summary>Whether to run a custom command instead of the shell</summary>
      <description>If true, the value of the custom_command setting will be used in place of running a shell.</description>
    </key>
    <key name="custom-command" type="s">
      <default>''</default>
      <summary>Custom command to use instead of the shell</summary>
      <description>Run this command in place of the shell, if use_custom_command is true.</description>
    </key>
    <!-- Encodings -->
    <key name="encoding" type="s">
      <choices>
        <choice value="ISO-8859-1" />
        <choice value="ISO-8859-2" />
        <choice value="ISO-8859-3" />
        <choice value="ISO-8859-4" />
        <choice value="ISO-8859-5" />
        <choice value="ISO-8859-6" />
        <choice value="ISO-8859-7" />
        <choice value="ISO-8859-8" />
        <choice value="ISO-8859-8-I" />
        <choice value="ISO-8859-9" />
        <choice value="ISO-8859-10" />
        <choice value="ISO-8859-13" />
        <choice value="ISO-8859-14" />
        <choice value="ISO-8859-15" />
        <choice value="ISO-8859-16" />
        <choice value="UTF-8" />
        <choice value="ARMSCII-8" />
        <choice value="BIG5" />
        <choice value="BIG5-HKSCS" />
        <choice value="CP866" />
        <choice value="EUC-JP" />
        <choice value="EUC-KR" />
        <choice value="EUC-TW" />
        <choice value="GB18030" />
        <choice value="GB2312" />
        <choice value="GBK" />
        <choice value="GEORGIAN-PS" />
        <choice value="IBM850" />
        <choice value="IBM852" />
        <choice value="IBM855" />
        <choice value="IBM857" />
        <choice value="IBM862" />
        <choice value="IBM864" />
        <choice value="ISO-2022-JP" />
        <choice value="ISO-2022-KR" />
        <choice value="ISO-IR-111" />
        <choice value="KOI8-R" />
        <choice value="KOI8-U" />
        <choice value="MAC_ARABIC" />
        <choice value="MAC_CE" />
        <choice value="MAC_CROATIAN" />
        <choice value="MAC-CYRILLIC" />
        <choice value="MAC_DEVANAGARI" />
        <choice value="MAC_FARSI" />
        <choice value="MAC_GREEK" />
        <choice value="MAC_GUJARATI" />
        <choice value="MAC_GURMUKHI" />
        <choice value="MAC_HEBREW" />
        <choice value="MAC_ICELANDIC" />
        <choice value="MAC_ROMAN" />
        <choice value="MAC_ROMANIAN" />
        <choice value="MAC_TURKISH" />
        <choice value="MAC_UKRAINIAN" />
        <choice value="SHIFT_JIS" />
        <choice value="TCVN" />
        <choice value="TIS-620" />
        <choice value="UHC" />
        <choice value="VISCII" />
        <choice value="WINDOWS-1250" />
        <choice value="WINDOWS-1251" />
        <choice value="WINDOWS-1252" />
        <choice value="WINDOWS-1253" />
        <choice value="WINDOWS-1254" />
        <choice value="WINDOWS-1255" />
        <choice value="WINDOWS-1256" />
        <choice value="WINDOWS-1257" />
        <choice value="WINDOWS-1258" />
      </choices>
      <default>'UTF-8'</default>
      <summary>Which encoding to use</summary>
    </key>
    <key name='automatic-switch' type='as'>
      <default>[]</default>
      <summary>Automatically make this profile active when match detected</summary>
      <description>Profile becomes active automatically when the current hostname and directory match one of the hostname:value pairs in this list.</description>
    </key>
    <key name="custom-hyperlinks" type="as">
      <default>[]</default>
      <summary>A list of custom links that can be clicked on in the terminal</summary>
      <description>A list of custom links, each link consists of a regex and optional launcher encoded according to CSV rules.</description>
    </key>
    <key name="triggers" type="as">
      <default>[]</default>
      <summary>A list of definitions that trigger actions based on content matches</summary>
      <description>A list of trigger definitions with each definition consisting of a regex, action to trigger and parameter.</description>
    </key>
    <!-- Badges -->
    <key name="badge-text" type="s">
      <default>''</default>
      <summary>Whether to display a background badge</summary>
      <description>When set, a badge will be rendered in the background of the terminal</description>
    </key>
    <key name="badge-color" type="s">
      <default>'#ffffff'</default>
      <summary>Badge colour</summary>
      <description>Custom color for the badge, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if badge-color-set is false.</description>
    </key>
    <key name="badge-color-set" type="b">
      <default>false</default>
      <summary>Whether to use custom badge color</summary>
      <description>If true, use the badge color from the profile.</description>
    </key>
    <key name="badge-position" enum="com.gexperts.Tilix.Quadrant">
      <default>'northeast'</default>
      <summary>The position where the badge is displayed</summary>
      <description>Determines the position based on quadrants whre the badge is rendered in the terminal.</description>
    </key>
    <key name="badge-use-system-font" type="b">
      <default>true</default>
      <summary>Whether to use the system monospace font</summary>
    </key>
    <key name="badge-font" type="s">
      <default>'Monospace 12'</default>
      <summary>A Pango font name and size</summary>
    </key>

    <!-- Notify silent -->
    <key name="notify-silence-enabled" type="b">
      <default>false</default>
      <summary>Enable monitoring activity by default</summary>
      <description>When enabled, new terminals will automatically monitor activity by default.</description>
    </key>
    <key name="notify-silence-threshold" type="i">
      <default>0</default>
      <summary>The threshold for no activity before new activity notifies</summary>
      <description>When the threshold is non-zero, the terminal will output a notification if output is detected after a period of silence exceeds the threshold.</description>
    </key>
    <!-- Bold Color-->
    <key name="bold-color" type="s">
      <default>'#ffffff'</default>
      <summary>Bold colour</summary>
      <description>Custom color for bold text, as a color specification (can be HTML-style hex digits, or a color name such as "red"). This is ignored if bold-color-set is false.</description>
    </key>
    <key name="bold-color-set" type="b">
      <default>false</default>
      <summary>Whether to use custom bold color</summary>
      <description>If true, use the bold color from the profile.</description>
    </key>
  </schema>

  <!-- Keybinding settings -->
  <!--
    Note that key names match to action names used by Tilix. Since
    GSettings doesn't allow the '.' in key names, the action name is divided
    between the prefix and id using the hyphen. The first hyphen is treated as
    the separator, subsequent hyphens are treated as part of the action id.

    The intent is to use localization on the prefix and id to show more
    friendly descriptors to users in the GUI that allows modifying these names.
  -->

  <schema id="com.gexperts.Tilix.Keybindings" path="/com/gexperts/Tilix/keybindings/">
    <key name="app-new-window" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;N'</default>
      <summary>Keyboard shortcut to create a new window</summary>
    </key>
    <key name="app-new-session" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;T'</default>
      <summary>Keyboard shortcut to create a new session</summary>
    </key>
    <key name="app-preferences" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to open preferences</summary>
    </key>
    <key name="app-shortcuts" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to show shortcuts</summary>
    </key>
    <key name="session-synchronize-input" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to synchronize input for the session</summary>
    </key>
    <key name="session-close" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;Q'</default>
      <summary>Keyboard shortcut to close a session</summary>
    </key>
    <key name="session-name" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to change the name of the session</summary>
    </key>
    <key name="session-open" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;o'</default>
      <summary>Keyboard shortcut to open a previously saved session</summary>
    </key>
    <key name="session-save" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;s'</default>
      <summary>Keyboard shortcut to save the current session</summary>
    </key>
    <key name="session-save-as" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to save the current session as a new file</summary>
    </key>
    <key name="session-switch-to-terminal-0" type="s">
      <default>'&lt;Alt&gt;0'</default>
      <summary>Keyboard shortcut to switch to terminal 0</summary>
    </key>
    <key name="session-switch-to-terminal-1" type="s">
      <default>'&lt;Alt&gt;1'</default>
      <summary>Keyboard shortcut to switch to terminal 1</summary>
    </key>
    <key name="session-switch-to-terminal-2" type="s">
      <default>'&lt;Alt&gt;2'</default>
      <summary>Keyboard shortcut to switch to terminal 2</summary>
    </key>
    <key name="session-switch-to-terminal-3" type="s">
      <default>'&lt;Alt&gt;3'</default>
      <summary>Keyboard shortcut to switch to terminal 3</summary>
    </key>
    <key name="session-switch-to-terminal-4" type="s">
      <default>'&lt;Alt&gt;4'</default>
      <summary>Keyboard shortcut to switch to terminal 4</summary>
    </key>
    <key name="session-switch-to-terminal-5" type="s">
      <default>'&lt;Alt&gt;5'</default>
      <summary>Keyboard shortcut to switch to terminal 5</summary>
    </key>
    <key name="session-switch-to-terminal-6" type="s">
      <default>'&lt;Alt&gt;6'</default>
      <summary>Keyboard shortcut to switch to terminal 6</summary>
    </key>
    <key name="session-switch-to-terminal-7" type="s">
      <default>'&lt;Alt&gt;7'</default>
      <summary>Keyboard shortcut to switch to terminal 7</summary>
    </key>
    <key name="session-switch-to-terminal-8" type="s">
      <default>'&lt;Alt&gt;8'</default>
      <summary>Keyboard shortcut to switch to terminal 8</summary>
    </key>
    <key name="session-switch-to-terminal-9" type="s">
      <default>'&lt;Alt&gt;9'</default>
      <summary>Keyboard shortcut to switch to terminal 9</summary>
    </key>
    <key name="session-switch-to-terminal-up" type="s">
      <default>'&lt;Alt&gt;Up'</default>
      <summary>Keyboard shortcut to switch to terminal above</summary>
    </key>
    <key name="session-switch-to-terminal-down" type="s">
      <default>'&lt;Alt&gt;Down'</default>
      <summary>Keyboard shortcut to switch to terminal below</summary>
    </key>
    <key name="session-switch-to-terminal-left" type="s">
      <default>'&lt;Alt&gt;Left'</default>
      <summary>Keyboard shortcut to switch to terminal left</summary>
    </key>
    <key name="session-switch-to-terminal-right" type="s">
      <default>'&lt;Alt&gt;Right'</default>
      <summary>Keyboard shortcut to switch to terminal right</summary>
    </key>
    <key name="session-resize-terminal-up" type="s">
      <default>'&lt;Shift&gt;&lt;Alt&gt;Up'</default>
      <summary>Keyboard shortcut to resize terminal up</summary>
    </key>
    <key name="session-resize-terminal-down" type="s">
      <default>'&lt;Shift&gt;&lt;Alt&gt;Down'</default>
      <summary>Keyboard shortcut to resize terminal down</summary>
    </key>
    <key name="session-resize-terminal-left" type="s">
      <default>'&lt;Shift&gt;&lt;Alt&gt;Left'</default>
      <summary>Keyboard shortcut to resize terminal left</summary>
    </key>
    <key name="session-resize-terminal-right" type="s">
      <default>'&lt;Shift&gt;&lt;Alt&gt;Right'</default>
      <summary>Keyboard shortcut to resize terminal right</summary>
    </key>
    <key name="session-add-right" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;r'</default>
      <summary>Keyboard shortcut to add new terminal right</summary>
    </key>
    <key name="session-add-down" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;d'</default>
      <summary>Keyboard shortcut to add new terminal down</summary>
    </key>
    <key name="session-add-auto" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;a'</default>
      <summary>Keyboard shortcut to add new terminal automatically</summary>
    </key>
    <key name="win-view-sidebar" type="s">
      <default>'F12'</default>
      <summary>Keyboard shortcut to view session sidebar</summary>
    </key>
    <key name="win-switch-to-session-0" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;0'</default>
      <summary>Keyboard shortcut to switch to session 10</summary>
    </key>
    <key name="win-switch-to-session-1" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;1'</default>
      <summary>Keyboard shortcut to switch to session 1</summary>
    </key>
    <key name="win-switch-to-session-2" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;2'</default>
      <summary>Keyboard shortcut to switch to session 2</summary>
    </key>
    <key name="win-switch-to-session-3" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;3'</default>
      <summary>Keyboard shortcut to switch to session 3</summary>
    </key>
    <key name="win-switch-to-session-4" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;4'</default>
      <summary>Keyboard shortcut to switch to session 4</summary>
    </key>
    <key name="win-switch-to-session-5" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;5'</default>
      <summary>Keyboard shortcut to switch to session 5</summary>
    </key>
    <key name="win-switch-to-session-6" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;6'</default>
      <summary>Keyboard shortcut to switch to session 6</summary>
    </key>
    <key name="win-switch-to-session-7" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;7'</default>
      <summary>Keyboard shortcut to switch to session 7</summary>
    </key>
    <key name="win-switch-to-session-8" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;8'</default>
      <summary>Keyboard shortcut to switch to session 8</summary>
    </key>
    <key name="win-switch-to-session-9" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;9'</default>
      <summary>Keyboard shortcut to switch to session 9</summary>
    </key>
    <key name="win-switch-to-next-session" type="s">
      <default>'&lt;Ctrl&gt;Page_Down'</default>
      <summary>Keyboard shortcut to switch to the next session</summary>
    </key>
    <key name="win-switch-to-previous-session" type="s">
      <default>'&lt;Ctrl&gt;Page_Up'</default>
      <summary>Keyboard shortcut to switch to the previous session</summary>
    </key>
    <key name="win-reorder-previous-session" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;Page_Up'</default>
      <summary>Keyboard shortcut to reorder to the previous session</summary>
    </key>
    <key name="win-reorder-next-session" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;Page_Down'</default>
      <summary>Keyboard shortcut to reorder to the next session</summary>
    </key>
    <key name="win-fullscreen" type="s">
      <default>'F11'</default>
      <summary>Keyboard shortcut to toggle fullscreen</summary>
    </key>
    <!-- Can't use accelerators with tab, may need to hardwire this in VTE -->
    <key name="session-switch-to-next-terminal" type="s">
      <default>'&lt;Ctrl&gt;Tab'</default>
      <summary>Keyboard shortcut to switch to the next terminal</summary>
    </key>
    <key name="session-switch-to-previous-terminal" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;Tab'</default>
      <summary>Keyboard shortcut to switch to the previous terminal</summary>
    </key>
    <key name="terminal-find" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;f'</default>
      <summary>Keyboard shortcut to find text in terminal</summary>
    </key>
    <key name="terminal-find-next" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;g'</default>
      <summary>Keyboard shortcut to find next match in terminal</summary>
    </key>
    <key name="terminal-find-previous" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;h'</default>
      <summary>Keyboard shortcut to find previous match in terminal</summary>
    </key>
    <key name="terminal-layout" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to set a customize layout options for the terminal</summary>
    </key>
    <key name="terminal-close" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;W'</default>
      <summary>Keyboard shortcut to close the terminal</summary>
    </key>
    <key name="terminal-maximize" type="s">
      <default>'&lt;Shift&gt;&lt;Ctrl&gt;X'</default>
      <summary>Keyboard shortcut to maximize or restore the terminal</summary>
    </key>
    <key name="terminal-profile-preference" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to edit the current profile</summary>
    </key>
    <key name="terminal-read-only" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to toggle whether the terminal is read-only</summary>
    </key>
    <key name="terminal-reset" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to reset the terminal states</summary>
    </key>
    <key name="terminal-reset-and-clear" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to reset the terminal states and clear the output buffer</summary>
    </key>
    <key name="terminal-copy" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;c'</default>
      <summary>Keyboard shortcut to copy selected text in terminal</summary>
    </key>
    <key name="terminal-copy-as-html" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to copy selected text in terminal as HTML</summary>
    </key>
    <key name="terminal-paste" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;v'</default>
      <summary>Keyboard shortcut to paste text in terminal from clipboard</summary>
    </key>
    <key name="terminal-paste-primary" type="s">
      <default>'&lt;Shift&gt;Insert'</default>
      <summary>Keyboard shortcut to paste text in terminal from primary selection</summary>
    </key>
    <key name="terminal-advanced-paste" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to paste text in terminal from clipboard via advanced paste dialog</summary>
    </key>
    <key name="terminal-select-all" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;a'</default>
      <summary>Keyboard shortcut to select all text in terminal</summary>
    </key>
    <key name="terminal-unselect-all" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to unselect all text in terminal</summary>
    </key>
    <key name="terminal-zoom-in" type="s">
      <default>'&lt;Ctrl&gt;plus'</default>
      <summary>Keyboard shortcut to make font larger</summary>
    </key>
    <key name="terminal-zoom-out" type="s">
      <default>'&lt;Ctrl&gt;minus'</default>
      <summary>Keyboard shortcut to make font smaller</summary>
    </key>
    <key name="terminal-zoom-normal" type="s">
      <default>'&lt;Ctrl&gt;0'</default>
      <summary>Keyboard shortcut to make font normal-size</summary>
    </key>
    <key name="terminal-save" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to save terminal contents</summary>
    </key>
    <key name="terminal-insert-number" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to insert the current terminal number</summary>
    </key>
    <key name="terminal-insert-password" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to insert a password into the terminal</summary>
    </key>
    <key name="terminal-title-style" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to cycle the terminal title styles</summary>
    </key>
    <key name="terminal-select-bookmark" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;b'</default>
      <summary>Keyboard shortcut to select a bookmark</summary>
    </key>
    <key name="terminal-add-bookmark" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to add a bookmark</summary>
    </key>
    <key name="terminal-scroll-up" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;Up'</default>
      <summary>Keyboard shortcut to scroll up</summary>
    </key>
    <key name="terminal-scroll-down" type="s">
      <default>'&lt;Ctrl&gt;&lt;Shift&gt;Down'</default>
      <summary>Keyboard shortcut to scroll down</summary>
    </key>
    <key name="terminal-page-up" type="s">
      <default>'&lt;Shift&gt;Page_Up'</default>
      <summary>Keyboard shortcut to page up</summary>
    </key>
    <key name="terminal-page-down" type="s">
      <default>'&lt;Shift&gt;Page_Down'</default>
      <summary>Keyboard shortcut to page down</summary>
    </key>
    <key name="terminal-monitor-silence" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to toggle silence monitor</summary>
    </key>
    <key name="terminal-sync-input-override" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to toggle input synchronization override</summary>
    </key>
    <key name="terminal-file-browser" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to open directory in file browser</summary>
    </key>
    <!-- Move between prompts -->
    <key name="terminal-next-prompt" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to move to next prompt</summary>
    </key>
    <key name="terminal-previous-prompt" type="s">
      <default>'disabled'</default>
      <summary>Keyboard shortcut to move to previous prompt</summary>
    </key>
    <key name="terminal-toggle-margin" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;m'</default>
      <summary>Keyboard shortcut to toggle margin display</summary>
    </key>
    <!-- Nautilus Shortcut for Open in Tilix -->
    <key name="nautilus-open" type="s">
      <default>'&lt;Ctrl&gt;&lt;Alt&gt;t'</default>
      <summary>Keyboard shortcut used in Nautilus for Open Here extension</summary>
    </key>
  </schema>
</schemalist>
