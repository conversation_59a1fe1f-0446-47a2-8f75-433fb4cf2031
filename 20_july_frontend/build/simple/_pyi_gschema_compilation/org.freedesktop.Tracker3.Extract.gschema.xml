<?xml version="1.0" encoding="UTF-8"?>
<!--
Copyright (C) 2011, Nokia <<EMAIL>>

This library is free software; you can redistribute it and/or
modify it under the terms of the GNU Lesser General Public
License as published by the Free Software Foundation; either
version 2.1 of the License, or (at your option) any later version.

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANT<PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public
License along with this library; if not, write to the
Free Software Foundation, Inc., 51 Franklin Street, Fifth Floor,
Boston, MA  02110-1301, USA.
-->
<schemalist>
  <schema id="org.freedesktop.Tracker3.Extract" path="/org/freedesktop/tracker/extract/" gettext-domain="tracker-miners">
    <key name="max-bytes" type="i">
      <summary>Max bytes to extract</summary>
      <description>Maximum number of UTF-8 bytes to extract.</description>
      <range min="0" max="10485760"/>
      <default>1048576</default>
    </key>

    <key name="text-allowlist" type="as">
      <summary>Text file allowlist</summary>
      <description>Filename patterns for plain text documents that should be indexed</description>
      <default>[ '*.txt', '*.md', '*.mdwn' ]</default>
    </key>


    <key name="wait-for-miner-fs" type="b">
      <summary>Wait for FS miner to be done before extracting</summary>
      <description>When true, tracker-extract will wait for tracker-miner-fs to be done crawling before extracting meta-data. This option is useful on constrained environment where it is important to list files as fast as possible and can wait to get meta-data later.</description>
      <default>false</default>
    </key>
  </schema>
</schemalist>
