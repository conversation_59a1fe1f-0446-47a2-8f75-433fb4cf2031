<?xml version="1.0" encoding="UTF-8"?>
<schemalist gettext-domain="gsettings-desktop-schemas">

  <schema id="org.gnome.desktop.a11y.magnifier"
	  path="/org/gnome/desktop/a11y/magnifier/">
    <key name="mouse-tracking" enum="org.gnome.desktop.GDesktopMagnifierMouseTrackingMode">
      <default>'proportional'</default>
      <summary>Mouse Tracking Mode</summary>
      <description>
        Determines the position of the magnified mouse image within the
        magnified view and how it reacts to system mouse movement. The values
        are
        • none: no mouse tracking;
        • centered: the mouse image is
        displayed at the center of the zoom region (which also represents
        the point under the system mouse) and the magnified contents are
        scrolled as the system mouse moves;
        • proportional: the position of the magnified mouse in the zoom region
        is proportionally the same as the position of the system mouse on screen;
        • push: when the magnified mouse intersects a boundary of the zoom
        region, the contents are scrolled into view.
      </description>
    </key>
    <key name="focus-tracking" enum="org.gnome.desktop.GDesktopMagnifierFocusTrackingMode">
      <default>'proportional'</default>
      <summary>Focus Tracking Mode</summary>
      <description>
        Determines the position of the focused widget within magnified view.

        The values are:

        • none: no focus tracking

        • centered: the focused image is displayed at the center of the zoom region (which also represents the
          point under the system focus) and the magnified contents are scrolled as the system focus moves

        • proportional: the position of the magnified focus in the zoom region is proportionally the same as the
          position of the system focus on screen

        • push: when the magnified focus intersects a boundary of the zoom region, the contents are scrolled
          into view
    </description>
    </key>
    <key name="caret-tracking" enum="org.gnome.desktop.GDesktopMagnifierCaretTrackingMode">
      <default>'centered'</default>
      <summary>Caret Tracking Mode</summary>
      <description>
        Determines the position of the caret within magnified view. The values are:

      • none: no caret tracking

      • centered: the image of the caret is displayed at the center of the zoom region (which also represents
        the point under the system caret) and the magnified contents are scrolled as the system caret moves

      • proportional: the position of the magnified caret in the zoom region is proportionally the same as the
        position of the system caret on screen

      • push: when the magnified caret intersects a boundary of the zoom region, the contents are scrolled into
        view
      </description>
    </key>
    <key name="screen-position" enum="org.gnome.desktop.GDesktopMagnifierScreenPosition">
      <default>'full-screen'</default>
      <summary>Screen position</summary>
      <description>
        The magnified view either fills the entire screen, or occupies the
        top-half, bottom-half, left-half, or right-half of the screen.
      </description>
    </key>
    <key name="mag-factor" type="d">
      <default>2.0</default>
      <range min="0.1" max="32.0"/>
      <summary>Magnification factor</summary>
      <description>
        The power of the magnification. A value of 1.0 means no magnification.
        A value of 2.0 doubles the size.
      </description>
    </key>
    <key name="lens-mode" type="b">
      <default>false</default>
      <summary>Enable lens mode</summary>
      <description>
        Whether the magnified view should be centered over the location of
        the system mouse and move with it.
      </description>
    </key>
    <key name="scroll-at-edges" type="b">
      <default>false</default>
      <summary>
        Scroll magnified contents beyond the edges of the desktop
      </summary>
      <description>
        For centered mouse tracking, when the system pointer is at or near the
        edge of the screen, the magnified contents continue to scroll such that
        the screen edge moves into the magnified view.
      </description>
    </key>

    <!-- Cross-hairs -->
    <key name="show-cross-hairs" type="b">
      <default>false</default>
      <summary>Show or hide crosshairs</summary>
      <description>
        Enables/disables display of crosshairs centered on the magnified
        mouse sprite.
      </description>
    </key>
    <key name="cross-hairs-thickness" type="i">
      <default>8</default>
      <summary>Thickness of the crosshairs in pixels</summary>
      <description>
        Width in pixels of the vertical and horizontal lines that make up the crosshairs.
      </description>
    </key>
    <key name="cross-hairs-color" type="s">
      <default>'#ff0000'</default>
      <summary>Color of the crosshairs</summary>
      <description>
        The color of the the vertical and horizontal lines that make up
        the crosshairs.
      </description>
    </key>
    <key name="cross-hairs-opacity" type="d">
      <default>0.66</default>
      <range min="0.0" max="1.0"/>
      <summary>Opacity of the crosshairs</summary>
      <description>
        Determines the transparency of the crosshairs, from fully opaque
        to fully transparent.
      </description>
    </key>
    <key name="cross-hairs-length" type="i">
      <default>4096</default>
      <range min="20" max="4096"/>
      <summary>Length of the crosshairs in pixels</summary>
      <description>
        Determines the length in pixels of the vertical and horizontal
        lines that make up the crosshairs.
      </description>
    </key>
    <key name="cross-hairs-clip" type="b">
      <default>false</default>
      <summary>Clip the crosshairs at the center</summary>
      <description>
        Determines whether the crosshairs intersect the magnified mouse sprite,
        or are clipped such that the ends of the horizontal and vertical lines
        surround the mouse image.
      </description>
    </key>
    <key name="invert-lightness" type="b">
      <default>false</default>
      <summary>Inverse lightness</summary>
      <description>
        Determines whether the lightness values are inverted:  darker colors
        become lighter and vice versa, and white and black are interchanged.
      </description>
    </key>
    <key name="color-saturation" type="d">
      <default>1.0</default>
      <range min="0.0" max="1.0"/>
      <summary>Color Saturation</summary>
      <description>
        Represents a change to the color saturation, from 0.0 (grayscale)
        to 1.0 (full color).
      </description>
    </key>
    <key name="brightness-red" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change brightness of red</summary>
      <description>
        Represents a change to the default brightness of the red component. Zero
        indicates no change, values less than zero indicate a decrease, and
        values greater than zero indicate an increase.
      </description>
    </key>
    <key name="brightness-green" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change brightness of green</summary>
      <description>
        Represents a change to the default brightness for the green component.
        Zero indicates no change, values less than zero indicate a decrease, and
        values greater than zero indicate an increase.
      </description>
    </key>
    <key name="brightness-blue" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change brightness of blue</summary>
      <description>
        Represents a change to the default brightness for the blue component.
        Zero indicates no change, values less than zero indicate a decrease, and
        values greater than zero indicate an increase.
      </description>
    </key>
    <key name="contrast-red" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change contrast of red</summary>
      <description>
        Represents a change to the default contrast of the red component.  Zero
        indicates no change in contrast, values less than zero indicate a
        decrease, and values greater than zero indicate an increase.
      </description>
    </key>
    <key name="contrast-green" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change contrast of green</summary>
      <description>
        Represents a change to the default contrast of the green component.
        Zero indicates no change in contrast, values less than zero indicate a
        decrease, and values greater than zero indicate an increase.
      </description>
    </key>
    <key name="contrast-blue" type="d">
      <default>0.0</default>
      <range min="-1.0" max="1.0"/>
      <summary>Change contrast of blue</summary>
      <description>
        Represents a change to the default contrast of the blue component.  Zero
        indicates no change in contrast, values less than zero indicate a
        decrease, and values greater than zero indicate an increase.
     </description>
    </key>
  </schema>
</schemalist>
