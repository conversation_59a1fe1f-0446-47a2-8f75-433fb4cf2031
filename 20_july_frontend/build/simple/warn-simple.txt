
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by /home/<USER>/.local/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py (delayed), /home/<USER>/.local/lib/python3.10/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py (delayed)
missing module named org - imported by copy (optional)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named urllib.quote - imported by urllib (optional), apport.packaging_impl (optional)
missing module named urllib.unquote - imported by urllib (conditional), apport.report (conditional), apport.packaging_impl (optional), lazr.restfulclient.resource (optional)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional), apport.report (conditional), apport.packaging_impl (optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional), lazr.restfulclient.resource (optional), wadllib.application (optional), lazr.restfulclient._browser (optional), launchpadlib.credentials (optional)
missing module named nt - imported by os (delayed, conditional, optional), ntpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), ctypes (delayed, conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named msvcrt - imported by subprocess (optional), click._winconsole (top-level), multiprocessing.spawn (delayed, conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), click._termui_impl (conditional), getpass (optional), filelock._windows (conditional), _plotly_utils.png (delayed, conditional), joblib.externals.loky.backend.popen_loky_win32 (top-level), joblib.externals.loky.backend.spawn (conditional), joblib.externals.loky.backend.resource_tracker (conditional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named _winapi - imported by encodings (delayed, conditional, optional), ntpath (optional), subprocess (optional), multiprocessing.connection (optional), multiprocessing.spawn (delayed, conditional), multiprocessing.reduction (conditional), multiprocessing.shared_memory (conditional), multiprocessing.heap (conditional), multiprocessing.popen_spawn_win32 (top-level), asyncio.windows_events (top-level), asyncio.windows_utils (top-level), mimetypes (optional), joblib.externals.loky.backend.popen_loky_win32 (top-level), joblib.externals.loky.backend.resource_tracker (conditional)
missing module named _overlapped - imported by asyncio.windows_events (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pep517 - imported by importlib.metadata (delayed), importlib_metadata (delayed)
missing module named winreg - imported by importlib._bootstrap_external (conditional), platform (delayed, optional), mimetypes (optional), urllib.request (delayed, conditional, optional), matplotlib.font_manager (delayed), matplotlib (delayed, conditional), requests.utils (delayed, conditional, optional), numba.misc.appdirs (delayed), torch._appdirs (delayed), setuptools._distutils.compilers.C.msvc (top-level), setuptools.msvc (conditional), setuptools._vendor.platformdirs.windows (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional), joblib.parallel (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (delayed, conditional, optional), skimage.util.apply_parallel (delayed, conditional, optional)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), torchvision.datasets.kinetics (top-level), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by numpy.random.bit_generator (top-level), torch.utils._backport_slots (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), git.objects.fun (conditional), streamlit.runtime.state.query_params (conditional), streamlit.runtime.state.query_params_proxy (conditional), nibabel.openers (conditional), setuptools._distutils.dist (conditional), pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named 'distutils.util' - imported by setuptools._core_metadata (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (top-level), setuptools.command.build_ext (delayed), setuptools._distutils.extension (delayed), setuptools._distutils.compilers.C.unix (delayed, conditional)
missing module named 'distutils.fancy_getopt' - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed)
missing module named 'distutils.debug' - imported by setuptools.dist (top-level), setuptools._distutils.compilers.C.base (delayed), setuptools._distutils.cmd (delayed), setuptools._distutils.filelist (delayed)
missing module named distutils.sysconfig - imported by distutils (delayed, conditional), setuptools._distutils.util (delayed, conditional), setuptools._distutils.extension (delayed), setuptools._distutils.command.build_ext (delayed), setuptools._distutils.compilers.C.cygwin (delayed)
missing module named distutils.file_util - imported by distutils (top-level), setuptools._distutils.command.sdist (top-level)
missing module named distutils.dir_util - imported by distutils (top-level), setuptools._distutils.command.sdist (top-level)
missing module named distutils.archive_util - imported by distutils (top-level), setuptools._distutils.command.sdist (top-level)
missing module named distutils.errors - imported by distutils (top-level), setuptools.errors (top-level), setuptools.config.expand (top-level), setuptools.extension (top-level), setuptools.dist (top-level), setuptools.archive_util (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.installer (top-level), setuptools.msvc (top-level)
missing module named distutils.log - imported by distutils (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools.command.bdist_egg (top-level), setuptools.command.sdist (top-level), setuptools.command.setopt (top-level), setuptools.command.egg_info (top-level), setuptools.wheel (delayed), setuptools.installer (top-level), setuptools._shutil (top-level), setuptools.command.bdist_wheel (top-level), setuptools.command.build_ext (top-level)
missing module named 'distutils.text_file' - imported by setuptools._distutils.extension (delayed), setuptools._distutils.sysconfig (delayed)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils.versionpredicate' - imported by setuptools._distutils.dist (delayed)
missing module named 'distutils.command' - imported by setuptools.command (top-level), setuptools.dist (top-level), setuptools.command.build (top-level), setuptools.command.sdist (top-level), setuptools.command.build_ext (conditional, optional), setuptools._distutils.dist (delayed)
missing module named 'distutils.cmd' - imported by setuptools.dist (top-level), setuptools._distutils.dist (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named ConfigParser - imported by apport.fileutils (optional), lazr.restfulclient.authorize.oauth (optional), apport_python_hook (delayed, optional)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional), launchpadlib.launchpad (optional), lazr.restfulclient.resource (optional), launchpadlib.credentials (optional), launchpadlib.uris (optional), apport.packaging_impl (delayed, conditional, optional)
missing module named httplib - imported by apport.packaging_impl (delayed, conditional, optional)
missing module named 'ctypes.macholib' - imported by ctypes.util (conditional)
missing module named gi.repository.Secret - imported by gi.repository (optional), keyring.backends.libsecret (optional)
runtime module named gi.repository.GLib - imported by matplotlib.cbook (delayed, conditional), gi.repository (delayed, conditional), gi._ossighelper (delayed), keyring.backends.libsecret (optional), gi.overrides.Gio (top-level), gi.overrides.GObject (top-level), gi.repository.GObject (top-level)
runtime module named gi.repository.Gio - imported by keyring.backends.libsecret (optional), gi.repository (optional)
runtime module named gi.repository.GObject - imported by gi.repository.Gio (top-level)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional), dbus.proxies (optional)
missing module named win32cred - imported by keyring.backends.Windows (optional)
missing module named pywintypes - imported by keyring.backends.Windows (optional)
missing module named 'win32ctypes.pywin32' - imported by keyring.backends.Windows (optional)
missing module named win32ctypes - imported by keyring.backends.Windows (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'six.moves.urllib.parse' - imported by lazr.restfulclient.authorize.oauth (top-level), launchpadlib.credentials (top-level)
missing module named StringIO - imported by six (conditional)
missing module named ca_certs_locater - imported by httplib2.certs (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named __builtin__ - imported by pyparsing (conditional)
missing module named ordereddict - imported by pyparsing (optional)
missing module named collections.Mapping - imported by collections (optional), pyparsing (optional), pytz.lazy (optional)
missing module named collections.MutableMapping - imported by collections (optional), pyparsing (optional)
missing module named collections.Iterable - imported by collections (optional), pyparsing (optional)
missing module named socks - imported by urllib3.contrib.socks (optional), httplib2 (optional)
missing module named cPickle - imported by apport.packaging_impl (optional)
missing module named UserDict - imported by pytz.lazy (optional), problem_report (conditional)
missing module named cStringIO - imported by apport_python_hook (delayed, optional), launchpadlib.credentials (optional)
missing module named 'distutils.spawn' - imported by setuptools._distutils.cmd (delayed)
missing module named 'distutils.dist' - imported by setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (top-level), setuptools._distutils.cmd (delayed, conditional)
missing module named 'distutils.core' - imported by setuptools.extension (top-level), setuptools.dist (top-level), setuptools (top-level), setuptools._distutils.dist (delayed)
missing module named 'distutils.extension' - imported by setuptools.extension (top-level)
missing module named 'distutils.filelist' - imported by setuptools.monkey (top-level), setuptools.command.egg_info (top-level)
missing module named 'distutils.dir_util' - imported by setuptools.command.bdist_egg (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'Cython.Distutils' - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'distutils.sysconfig' - imported by setuptools.command.build_ext (top-level)
missing module named 'distutils.ccompiler' - imported by setuptools.command.build_ext (top-level)
missing module named 'distutils.log' - imported by setuptools.logging (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), torch._jit_internal (optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional), triton.testing (delayed), torch.testing._internal.common_device_type (optional), joblib.externals.loky.backend.context (delayed, optional), joblib.externals.loky.backend.utils (optional), joblib.externals.loky.process_executor (optional), pyarrow.tests.util (delayed), torch._numpy.testing.utils (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named defusedxml - imported by PIL.Image (optional), tifffile.tifffile (delayed, optional)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
missing module named six.moves.winreg - imported by six.moves (top-level), dateutil.tz.win (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), six.moves.urllib (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named colorama - imported by click._compat (delayed, conditional), tornado.log (optional), tqdm.utils (conditional, optional), numba.core.errors (optional), torch._inductor.runtime.runtime_utils (optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional), scipy._lib._util (conditional), sklearn.utils.fixes (conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional), plotly.basewidget (top-level), pydeck.widget.widget (top-level), altair.jupyter.jupyter_chart (top-level)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), sympy.interactive.printing (delayed, optional), altair.utils.core (delayed, conditional), altair._magics (top-level)
missing module named IPython - imported by llvmlite.binding.analysis (delayed, conditional, optional), pandas.io.formats.printing (delayed), sympy.interactive.session (delayed, conditional, optional)
missing module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), torch.testing._internal.common_utils (delayed, conditional, optional), networkx.utils.backends (delayed, conditional, optional), torch.testing._internal.optests.generate_tests (delayed, conditional), sympy.testing.runtests_pytest (optional), skimage._shared.tester (delayed), nibabel (delayed), sklearn.utils._testing (optional), fsspec.conftest (top-level), pyarrow.conftest (top-level), pyarrow.tests.util (top-level), torch._numpy.testing.utils (delayed), skimage.filters.rank.tests.test_rank (top-level), skimage.data._fetchers (delayed, conditional), skimage._shared.testing (top-level)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed), sklearn.externals.array_api_compat.cupy._typing (top-level), sklearn.externals.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), narwhals._dask.expr (delayed), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.dask.array (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level), sklearn.externals.array_api_compat.dask.array.fft (top-level), sklearn.externals.array_api_compat.dask.array.linalg (top-level), skimage.util.apply_parallel (delayed, optional)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional), scipy._lib.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), narwhals._polars.dataframe (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._utils (delayed, conditional), joblib._dask (optional), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional), fsspec.implementations.dask (top-level), skimage.restoration._cycle_spin (optional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed), sklearn.externals.array_api_compat.common._helpers (delayed)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional), narwhals._pandas_like.series (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.cupy (top-level), sklearn.externals.array_api_compat.cupy._aliases (top-level), sklearn.externals.array_api_compat.cupy._info (top-level), sklearn.externals.array_api_compat.cupy._typing (top-level), sklearn.utils._testing (delayed, conditional), sklearn.externals.array_api_compat.cupy.fft (top-level), sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional), lxml.html.diff (optional), lxml.html._difflib (optional), pyarrow.conftest (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._laplacian (top-level), pandas.core.dtypes.common (delayed, conditional, optional), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), scipy._lib._array_api (delayed), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), sklearn.tree._classes (top-level), sklearn.ensemble._forest (top-level), sklearn.ensemble._gb (top-level), sklearn.ensemble._iforest (top-level), shap.explainers._linear (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level), networkx.utils.backends (delayed)
missing module named numpy.ComplexWarning - imported by numpy (conditional), scipy._lib._util (conditional), sklearn.utils.fixes (conditional)
missing module named numpy.AxisError - imported by numpy (conditional), scipy._lib._util (conditional), skimage.measure._blur_effect (optional), skimage.color.colorconv (optional)
missing module named scipy.special.elliprg - imported by scipy.special (top-level), skimage.draw.draw3d (top-level)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level), sklearn.mixture._bayesian_mixture (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.io._harwell_boeing.hb (top-level), scipy.optimize._milp (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.manifold._locally_linear (top-level), scipy.signal._fir_filter_design (top-level)
missing module named scipy.linalg.cho_solve_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.cholesky_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg.solve_banded - imported by scipy.linalg (top-level), scipy.spatial.transform._rotation_spline (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), sklearn.cluster._spectral (top-level), sklearn.manifold._locally_linear (top-level), scipy.signal._ltisys (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.gaussian_process._gpr (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level), scipy.linalg._decomp_polar (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level), sklearn.cluster._spectral (top-level), sklearn.manifold._locally_linear (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._optimize (top-level), scipy.optimize._minpack_py (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.gaussian_process._gpr (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._nonlin (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy._lib.cobyqa.models (top-level), sklearn.decomposition._kernel_pca (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._spectral_embedding (top-level)
missing module named scipy.optimize.root_scalar - imported by scipy.optimize (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._stats_py (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.optimize.brentq - imported by scipy.optimize (delayed), scipy.integrate._ivp.ivp (delayed), scipy.stats._binomtest (top-level), scipy.stats._odds_ratio (top-level)
missing module named scipy.optimize.OptimizeResult - imported by scipy.optimize (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.ivp (top-level), scipy._lib.cobyqa.main (top-level), scipy._lib.cobyqa.problem (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._lsq.bvls (top-level), scipy.optimize._spectral (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level), scipy.optimize._qap (top-level), scipy.optimize._direct_py (top-level)
missing module named scipy.optimize.minimize_scalar - imported by scipy.optimize (top-level), scipy.interpolate._bsplines (top-level), scipy.stats._multicomp (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.linalg.eig - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named scipy.linalg.lstsq - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.signal._fir_filter_design (top-level), scipy.signal._savitzky_golay (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named cffi - imported by scipy._lib._ccallback (delayed, optional), numba.core.typing.cffi_utils (optional), numba.core.ccallback (delayed), pyarrow.cffi (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional), tifffile._imagecodecs (delayed, optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named yarl - imported by fsspec.implementations.http (top-level), fsspec.implementations.http_sync (optional)
missing module named aiohttp - imported by fsspec.implementations.http (top-level)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), pydeck.io.html (delayed), altair.vegalite.v5.display (delayed), altair.vegalite.v5.api (delayed, conditional), shap.plots._force (optional), shap.plots._image (optional), shap.plots._text (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), plotly.graph_objects (delayed, conditional, optional), plotly.graph_objs (delayed, conditional, optional), pydeck.widget.widget (top-level)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named llvmlite.binding.parse_assembly - imported by llvmlite.binding (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level), numba.cpython.unicode (top-level), numba.np.arrayobj (top-level), numba.np.ufunc.wrappers (top-level), numba.cpython.old_mathimpl (top-level), numba.cpython.old_numbers (top-level), numba.cpython.new_mathimpl (top-level), numba.cpython.new_numbers (top-level)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), pandas.core._numba.extensions (delayed)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named coverage - imported by numba.misc.coverage_support (optional), numba.tests.support (optional)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'win32com.shell' - imported by numba.misc.appdirs (conditional, optional), torch._appdirs (delayed, conditional, optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional), torch._appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named win32api - imported by numba.misc.appdirs (delayed, conditional, optional), torch._appdirs (delayed, conditional, optional)
missing module named win32com - imported by numba.misc.appdirs (delayed)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named graphviz - imported by streamlit.type_util (conditional), llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed), streamlit.elements.graphviz_chart (conditional)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named 'pygments.style' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.token' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.lexer' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.styles' - imported by numba.misc.dump_style (top-level)
missing module named 'pygments.formatters' - imported by numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named 'pygments.lexers' - imported by numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments - imported by numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.annotations.pretty_annotate (delayed, optional), numba.core.utils (delayed, conditional, optional)
missing module named xmlrunner - imported by numba.testing (delayed, conditional), torch.testing._internal.common_utils (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named Queue - imported by numba.testing.main (optional)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), pyarrow (optional), tqdm.version (optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed), streamlit.connections.sql_connection (conditional)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional), streamlit.connections.sql_connection (delayed)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named bs4 - imported by pandas.io.html (delayed), lxml.html.soupparser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named 'html5lib.treebuilders' - imported by lxml.html.html5parser (top-level)
missing module named html5lib - imported by lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'cython.cimports' - imported by lxml.html.diff (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named array_api_compat - imported by sklearn.externals.array_api_extra._lib._utils._compat (optional)
missing module named 'cupy_backends.cuda' - imported by sklearn.externals.array_api_compat.common._helpers (delayed)
missing module named sklearn.externals.array_api_compat.common.array_namespace - imported by sklearn.externals.array_api_compat.common (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level)
missing module named torch.outer - imported by torch (top-level), sklearn.externals.array_api_compat.torch.linalg (top-level)
missing module named 'cupy.linalg' - imported by sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named 'cupy.fft' - imported by sklearn.externals.array_api_compat.cupy.fft (top-level)
missing module named array_api_strict - imported by sklearn.utils._array_api (delayed, conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named numpy.byte_bounds - imported by numpy (delayed, optional), joblib._memmapping_reducer (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed), skimage._shared.utils (delayed, optional)
missing module named numpydoc - imported by sklearn.utils._testing (delayed, optional)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named xgboost - imported by shap.utils._clustering (delayed), shap.explainers._tree (delayed, conditional)
missing module named catboost - imported by shap.explainers._tree (delayed, conditional)
missing module named pyspark - imported by narwhals.dependencies (conditional), narwhals._utils (delayed, conditional), shap.explainers._tree (optional)
missing module named tensorflow - imported by tensorboard.compat (delayed, optional), shap.models._teacher_forcing (delayed, conditional), shap.models._text_generation (delayed, conditional, optional), shap.models._topk_lm (delayed, conditional), shap.explainers._deep.deep_tf (top-level), shap.explainers.tf_utils (delayed, conditional), shap.explainers._gradient (delayed, conditional, optional), shap.explainers._kernel (delayed)
missing module named 'tensorflow.keras' - imported by shap.explainers._gradient (delayed)
missing module named 'tensorflow.python' - imported by tensorboard.plugins.audio.summary_v2 (delayed, conditional), streamlit.elements.write (delayed, conditional), shap.explainers._deep.deep_tf (top-level), shap.explainers.tf_utils (delayed, conditional), torch.contrib._tensorboard_vis (optional)
missing module named transformers - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.dynamo_graph_extractor (delayed, optional), shap.utils.transformers (delayed, conditional), shap.models._topk_lm (delayed), torch.testing._internal.common_distributed (delayed, optional)
missing module named 'lime.lime_tabular' - imported by shap.explainers.other._lime (optional)
missing module named lime - imported by shap.explainers.other._lime (optional)
missing module named shap._cext_gpu - imported by shap (optional), shap.explainers._gpu_tree (optional)
missing module named pyzstd - imported by nibabel._compression (conditional)
missing module named indexed_gzip - imported by nibabel._compression (conditional)
missing module named h5py - imported by torchvision.datasets.pcam (delayed, optional), nibabel.minc2 (delayed)
missing module named pydicom - imported by nibabel.nifti1 (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'tensorflow.compat' - imported by tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed)
missing module named boto3 - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named 'botocore.exceptions' - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named onnx - imported by torch.utils.tensorboard._onnx_graph (delayed), torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (conditional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (conditional), torch.onnx._internal._exporter_legacy (delayed, optional), torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx.verification (delayed, optional)
missing module named wcwidth - imported by tabulate (optional)
missing module named dill - imported by torch.utils._import_utils (delayed), torch.utils.data.graph (delayed, conditional, optional)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named av - imported by torchvision.io.video (optional), torchvision.io.video_reader (optional), imageio.plugins.pyav (top-level)
missing module named accimage - imported by torchvision.transforms.transforms (optional), torchvision.transforms.functional (optional), torchvision.transforms._functional_pil (optional), torchvision.datasets.folder (delayed)
missing module named 'av.video' - imported by torchvision.io.video (delayed, optional)
missing module named torchvision_extra_decoders - imported by torchvision.io.image (delayed, optional)
missing module named pycocotools - imported by torchvision.datasets.coco (delayed), torchvision.tv_tensors._dataset_wrapper (delayed)
missing module named gdown - imported by torchvision.datasets.utils (delayed, optional)
missing module named 'defusedxml.ElementTree' - imported by torchvision.datasets.voc (optional)
missing module named lmdb - imported by torchvision.datasets.lsun (delayed)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named redis - imported by triton.runtime.cache (delayed), torch._inductor.remote_cache (optional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.cuda._memory_viz (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'IPython.lib' - imported by sympy.interactive.printing (delayed, optional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named optree - imported by torch.utils._cxx_pytree (top-level), torch._dynamo.polyfills.pytree (conditional)
missing module named 'hypothesis.strategies' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named hypothesis - imported by torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named pytest_subtests - imported by torch.testing._internal.opinfo.core (delayed, conditional, optional)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.device_mesh (conditional), torch.distributed.distributed_c10d (top-level), torch._inductor.codegen.wrapper (delayed, optional), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.distributed.tensor._collective_utils (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed._symmetric_memory (top-level), torch.distributed.constants (top-level), torch.distributed._tools.fake_collectives (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.common_distributed (top-level), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named objgraph - imported by torch.testing._internal.common_utils (delayed, conditional, optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.distributed._symmetric_memory (top-level), torch.autograd (top-level), torch.testing._internal.common_distributed (top-level)
missing module named torch.tensor - imported by torch (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch.fx.passes.utils.common (top-level), torch.distributed.nn.api.remote_module (top-level), torch._dynamo.mutation_guard (top-level), torch.fx.experimental.proxy_tensor (top-level)
missing module named onnxruntime - imported by torch.onnx._internal.exporter._onnx_program (delayed, conditional), torch.onnx._internal.onnxruntime (delayed, conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx.verification (delayed, optional)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.utils (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (delayed, conditional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named safetensors - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.serialization (delayed)
missing module named onnxscript - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib._tensor_typing (top-level), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._verification (conditional), torch.onnx._internal.exporter._reporting (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter._ir_passes (delayed, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (delayed), torch.onnx._internal.exporter._building (top-level)
missing module named 'onnxscript.onnx_opset' - imported by torch.onnx._internal.exporter._torchlib.ops.core (top-level)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._lazy_import (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named astunparse - imported by torch.jit.frontend (optional)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), torch._functorch.partitioners (delayed), torch.fx.passes.graph_drawer (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional)
missing module named torch_xla - imported by torch.distributed.tensor._api (delayed, conditional, optional), torch._tensor (delayed, conditional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.distributed.ReduceOp - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level)
missing module named torch.distributed.group - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), torch.distributed.algorithms.model_averaging.utils (top-level)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (optional), torch.distributed.elastic.rendezvous.etcd_store (optional), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (optional), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'safetensors.torch' - imported by torch.distributed.checkpoint.filesystem (delayed, conditional, optional), torch.distributed.checkpoint._hf_storage (delayed)
missing module named huggingface_hub - imported by torch.distributed.checkpoint._hf_storage (delayed)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pulp - imported by torch.distributed._tools.sac_ilp (optional)
missing module named pwlf - imported by torch.distributed._tools.sac_estimator (delayed, optional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named opt_einsum - imported by torch.backends.opt_einsum (optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._higher_order_ops.cond (top-level), torch._subclasses.fake_tensor (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'torch._inductor.fb' - imported by torch._inductor.remote_cache (delayed, conditional, optional), torch._dynamo.pgo (delayed, optional), torch._inductor.cpp_builder (conditional), torch._inductor.compile_fx (conditional), torch._inductor.graph (conditional), torch._functorch._aot_autograd.autograd_cache (delayed, optional), torch._inductor.runtime.autotune_cache (delayed, optional), torch._inductor.codecache (conditional), torch._inductor.utils (delayed, optional), torch._dynamo.utils (delayed, conditional, optional)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional), torch._inductor.codegen.rocm.ck_conv_template (optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.types (top-level), torch._dynamo.convert_frame (top-level), torch._inductor.fx_passes.reinplace (top-level), torch._functorch._aot_autograd.input_output_analysis (top-level), torch._dynamo.decorators (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._inductor.compile_worker.subproc_pool (delayed, conditional), torch._inductor.codegen.rocm.compile_command (delayed, conditional), torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'ck4inductor.grouped_conv_fwd' - imported by torch._inductor.codegen.rocm.ck_conv_template (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named cutlass_generator - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named cutlass - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named 'triton._C.libtriton.ir' - imported by torch._higher_order_ops.triton_kernel_wrap (conditional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._inductor.config (conditional), torch._functorch.config (conditional), torch._dynamo.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named '_pytest.recwarn' - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named _pytest - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named torch._dynamo.variables.symbolic_convert - imported by torch._dynamo.variables.base (conditional)
missing module named 'torch_xla.core' - imported by torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named 'optree._C' - imported by torch._dynamo.polyfills.pytree (conditional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, conditional, optional)
missing module named einops - imported by torch._dynamo.decorators (delayed, conditional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch_xla.utils' - imported by torch._tensor (delayed, conditional)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch._library.infer_schema (top-level), torch.cuda (top-level), torch._inductor.graph (top-level), torch.distributed.nn.api.remote_module (top-level), torch.xpu (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named polars - imported by narwhals.dependencies (conditional), narwhals._utils (conditional), narwhals.schema (delayed, conditional), narwhals._compliant.series (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals.stable.v1.dependencies (conditional), narwhals._pandas_like.series (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._polars.dataframe (top-level), narwhals._polars.namespace (top-level), narwhals._polars.expr (top-level), narwhals._polars.utils (top-level), narwhals._polars.series (top-level), narwhals._dask.dataframe (delayed, conditional), narwhals._duckdb.dataframe (delayed, conditional), narwhals._arrow.series (delayed, conditional), narwhals.series (conditional), narwhals.dataframe (conditional), narwhals._compliant.dataframe (conditional), narwhals._namespace (conditional), narwhals._ibis.dataframe (delayed, conditional), narwhals._spark_like.dataframe (delayed, conditional), streamlit.dataframe_util (delayed, conditional), streamlit.runtime.caching.hashing (delayed, conditional)
missing module named dask_expr - imported by narwhals._dask.utils (conditional, optional), narwhals._dask.group_by (conditional, optional)
missing module named 'modin.pandas' - imported by narwhals.stable.v1.dependencies (conditional), narwhals._pandas_like.dataframe (delayed, conditional)
missing module named 'polars.lazyframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'polars.dataframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'duckdb.typing' - imported by narwhals._duckdb.utils (conditional), narwhals._duckdb.expr (top-level), narwhals._duckdb.namespace (top-level), narwhals._duckdb.dataframe (conditional)
missing module named duckdb - imported by narwhals.dependencies (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._duckdb.dataframe (top-level), narwhals._duckdb.utils (top-level), narwhals._duckdb.expr (top-level), narwhals._duckdb.expr_dt (top-level), narwhals._duckdb.expr_list (top-level), narwhals._duckdb.expr_str (top-level), narwhals._duckdb.expr_struct (top-level), narwhals._duckdb.namespace (top-level), narwhals._duckdb.selectors (conditional), narwhals._duckdb.group_by (conditional), narwhals._duckdb.series (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._namespace (conditional)
missing module named 'pyarrow._stubs_typing' - imported by narwhals._arrow.typing (conditional)
missing module named 'pyarrow.__lib_pxi' - imported by narwhals._arrow.typing (conditional)
missing module named 'dask.dataframe' - imported by narwhals.dependencies (conditional), narwhals._dask.namespace (top-level), narwhals.stable.v1.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._dask.dataframe (top-level), narwhals._dask.utils (conditional, optional), narwhals._dask.expr_dt (conditional), narwhals._dask.expr_str (top-level), narwhals._dask.expr (conditional), narwhals._dask.group_by (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._dask.selectors (conditional)
missing module named 'sqlframe.base' - imported by narwhals._spark_like.utils (delayed, conditional), narwhals._spark_like.expr_dt (conditional), narwhals._spark_like.expr_str (conditional), narwhals._spark_like.expr_struct (conditional), narwhals._spark_like.expr (delayed, conditional), narwhals._spark_like.selectors (conditional), narwhals._spark_like.namespace (delayed, conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._spark_like.group_by (conditional), narwhals.dependencies (delayed, conditional)
missing module named 'ibis.common' - imported by narwhals._ibis.utils (conditional)
missing module named 'ibis.selectors' - imported by narwhals._ibis.dataframe (delayed)
missing module named 'ibis.expr' - imported by narwhals._ibis.namespace (top-level), narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr_dt (conditional), narwhals._ibis.expr_str (top-level), narwhals._ibis.expr_struct (conditional), narwhals._ibis.expr (conditional), narwhals._ibis.group_by (conditional), narwhals._ibis.selectors (conditional)
missing module named ibis - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional), narwhals._ibis.namespace (top-level), narwhals._ibis.dataframe (top-level), narwhals._ibis.utils (top-level), narwhals._ibis.expr (top-level), narwhals._ibis.expr_str (top-level)
missing module named 'pyspark.sql' - imported by narwhals.dependencies (delayed, conditional, optional), narwhals._namespace (conditional), narwhals._spark_like.utils (delayed, conditional)
missing module named modin - imported by narwhals.dependencies (conditional)
missing module named cudf - imported by narwhals.dependencies (conditional), narwhals.stable.v1.dependencies (conditional)
missing module named sqlframe - imported by narwhals._utils (delayed, conditional)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named jsonpointer - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named 'vegafusion.runtime' - imported by altair.utils._vegafusion_data (conditional)
missing module named altair.vegalite.SCHEMA_VERSION - imported by altair.vegalite (delayed), altair.utils._importers (delayed)
missing module named vl_convert - imported by altair.utils._importers (delayed, optional)
missing module named vegafusion - imported by altair.utils._importers (delayed, optional)
missing module named altair.vegalite.v5.SCHEMA_VERSION - imported by altair.vegalite.v5 (delayed), altair.vegalite.v5.compiler (delayed)
missing module named anywidget - imported by plotly.basewidget (top-level), altair.jupyter (optional), altair.jupyter.jupyter_chart (top-level)
missing module named altair.VConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.VConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpecWithFrame - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelVConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelLayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelHConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelFacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.NonNormalizedSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetedUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.Chart - imported by altair (delayed), altair.vegalite.v5.display (delayed), altair.utils._transformed_data (top-level)
missing module named altair.renderers - imported by altair (delayed), altair.utils.mimebundle (delayed)
missing module named altair.vegalite_compilers - imported by altair (delayed), altair.utils._vegafusion_data (delayed)
missing module named altair.data_transformers - imported by altair (delayed), altair.utils._vegafusion_data (delayed), altair.utils._transformed_data (top-level)
missing module named altair.SchemaBase - imported by altair (conditional), altair.vegalite.v5.schema.channels (conditional)
missing module named altair.Parameter - imported by altair (conditional), altair.vegalite.v5.schema.core (conditional), altair.vegalite.v5.schema.channels (conditional), altair.vegalite.v5.schema.mixins (conditional)
missing module named xarray - imported by plotly.express._imshow (optional), streamlit.dataframe_util (delayed, conditional)
missing module named skimage.measure.label - imported by skimage.measure (top-level), skimage.restoration.inpaint (top-level)
missing module named skimage.measure.block_reduce - imported by skimage.measure (top-level), skimage.transform._warps (top-level)
missing module named skimage.exposure.histogram - imported by skimage.exposure (top-level), skimage.filters.thresholding (top-level)
missing module named skimage.exposure.is_low_contrast - imported by skimage.exposure (top-level), skimage.io._io (top-level), skimage.io._plugins.matplotlib_plugin (top-level)
missing module named skimage.color.rgba2rgb - imported by skimage.color (delayed, conditional), skimage.exposure.exposure (delayed, conditional)
missing module named skimage.color.rgb2gray - imported by skimage.color (top-level), skimage.measure._blur_effect (top-level), skimage.exposure.exposure (delayed, conditional)
missing module named skimage.color.gray2rgb - imported by skimage.color (top-level), skimage.feature._daisy (top-level), skimage.feature.haar (top-level), skimage.feature.texture (top-level)
missing module named skimage.transform.integral_image - imported by skimage.transform (top-level), skimage.feature.corner (top-level), skimage.filters.thresholding (top-level), skimage.feature.blob (top-level), skimage.feature.censure (top-level)
missing module named skimage.transform.rescale - imported by skimage.transform (top-level), skimage.feature.sift (top-level)
missing module named skimage.transform.pyramid_gaussian - imported by skimage.transform (top-level), skimage.feature.orb (top-level)
missing module named skimage.draw.rectangle - imported by skimage.draw (top-level), skimage.feature.haar (top-level)
missing module named skimage.transform.warp - imported by skimage.transform (top-level), skimage.filters._window (top-level)
missing module named pooch - imported by skimage.data._fetchers (delayed, optional)
missing module named 'zarr.indexing' - imported by tifffile.tifffile (delayed, optional)
missing module named 'zarr.hierarchy' - imported by tifffile.tifffile (delayed, optional)
missing module named zarr - imported by tifffile.tifffile (delayed)
missing module named _imagecodecs - imported by tifffile.tifffile (delayed, conditional, optional)
missing module named imagecodecs - imported by tifffile.tifffile (optional), imageio.plugins._tifffile (delayed, conditional, optional)
missing module named SimpleITK - imported by skimage.io._plugins.simpleitk_plugin (optional), imageio.plugins.simpleitk (delayed, optional)
missing module named imread - imported by skimage.io._plugins.imread_plugin (optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named 'osgeo.gdal' - imported by imageio.plugins.gdal (delayed, optional)
missing module named 'astropy.io' - imported by imageio.plugins.fits (delayed, optional)
missing module named imageio_ffmpeg - imported by imageio.plugins.ffmpeg (top-level)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named osgeo - imported by skimage.io._plugins.gdal_plugin (optional)
missing module named astropy - imported by skimage.io._plugins.fits_plugin (optional)
missing module named skimage.metrics.mean_squared_error - imported by skimage.metrics (top-level), skimage.restoration.j_invariant (top-level)
missing module named pywt - imported by skimage.restoration._denoise (delayed, optional)
missing module named skimage.filters.sobel - imported by skimage.filters (delayed), skimage.measure._blur_effect (delayed)
missing module named 'google.colab' - imported by plotly.io._renderers (conditional, optional)
missing module named BaseHTTPServer - imported by plotly.io._base_renderers (optional)
missing module named 'statsmodels.api' - imported by plotly.express.trendline_functions (delayed)
missing module named statsmodels - imported by plotly.express.trendline_functions (delayed)
missing module named plotly.colors.sequential - imported by plotly.colors (top-level), plotly.express._core (top-level)
missing module named plotly.colors.qualitative - imported by plotly.colors (top-level), plotly.express._core (top-level)
missing module named plotly.colors.validate_scale_values - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.validate_colorscale - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.validate_colors_dict - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.validate_colors - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.unlabel_rgb - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.unconvert_from_RGB_255 - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.n_colors - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.label_rgb - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.hex_to_rgb - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.find_intermediate_color - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.convert_to_RGB_255 - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.colorscale_to_scale - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.colorscale_to_colors - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.color_parser - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.PLOTLY_SCALES - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named plotly.colors.DEFAULT_PLOTLY_COLORS - imported by plotly.colors (top-level), plotly.figure_factory.utils (top-level)
missing module named 'plotly.version' - imported by plotly (conditional)
missing module named choreographer - imported by plotly.io._kaleido (delayed, conditional)
missing module named 'kaleido.errors' - imported by plotly.io._kaleido (delayed, conditional)
missing module named 'kaleido.scopes' - imported by plotly.io._kaleido (conditional, optional)
missing module named kaleido - imported by plotly.io._kaleido (delayed, conditional, optional)
missing module named _watchdog_fsevents - imported by watchdog.observers.fsevents (top-level)
missing module named 'bokeh.embed' - imported by streamlit.elements.bokeh_chart (delayed)
missing module named bokeh - imported by streamlit.elements.bokeh_chart (delayed, conditional)
missing module named 'rich.console' - imported by streamlit.error_util (delayed)
missing module named 'rich.panel' - imported by streamlit.error_util (delayed)
missing module named rich - imported by streamlit.error_util (delayed), streamlit.config (delayed, optional)
missing module named 'authlib.integrations' - imported by streamlit.web.server.oidc_mixin (top-level), streamlit.web.server.authlib_tornado_integration (top-level)
missing module named 'authlib.jose' - imported by streamlit.auth_util (delayed, optional)
missing module named authlib - imported by streamlit.auth_util (delayed, optional)
missing module named sniffio - imported by tenacity.asyncio (delayed, conditional)
missing module named trio - imported by tenacity.asyncio (delayed, conditional)
missing module named 'sqlalchemy.exc' - imported by streamlit.connections.sql_connection (delayed)
missing module named 'sqlalchemy.orm' - imported by streamlit.connections.sql_connection (delayed, conditional)
missing module named snowflake - imported by streamlit.connections.util (delayed, optional)
missing module named 'snowflake.snowpark' - imported by streamlit.connections.snowflake_connection (delayed, conditional), streamlit.connections.snowpark_connection (delayed, conditional)
missing module named 'snowflake.connector' - imported by streamlit.connections.snowflake_connection (delayed, conditional)
missing module named tkinter - imported by /home/<USER>/mri_frontend/20_july_frontend/simple_launcher.py (top-level), tifffile.tifffile (delayed), imageio.plugins._tifffile (delayed, optional)
