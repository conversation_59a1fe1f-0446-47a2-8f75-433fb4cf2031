@echo off
setlocal enabledelayedexpansion

REM Demetify - One-Click Start (Windows)
REM Checks installations, installs if needed, then starts the program

title Demetify - AI Alzheimer's Assessment System

echo.
echo  ██████╗ ███████╗███╗   ███╗███████╗████████╗██╗███████╗██╗   ██╗
echo  ██╔══██╗██╔════╝████╗ ████║██╔════╝╚══██╔══╝██║██╔════╝╚██╗ ██╔╝
echo  ██║  ██║█████╗  ██╔████╔██║█████╗     ██║   ██║█████╗   ╚████╔╝ 
echo  ██║  ██║██╔══╝  ██║╚██╔╝██║██╔══╝     ██║   ██║██╔══╝    ╚██╔╝  
echo  ██████╔╝███████╗██║ ╚═╝ ██║███████╗   ██║   ██║██║        ██║   
echo  ╚═════╝ ╚══════╝╚═╝     ╚═╝╚══════╝   ╚═╝   ╚═╝╚═╝        ╚═╝   
echo.
echo  AI-Powered Alzheimer's Disease Assessment System
echo  University of Illinois at Urbana-Champaign
echo  Project Lead: Prof. S. Seshadri
echo.
echo ================================================================
echo.

REM Check if already installed and working
if exist "launch_demetify.bat" (
    echo [INFO] Checking existing installation...
    
    REM Try to activate environment and test
    if exist "%USERPROFILE%\miniconda3\Scripts\activate.bat" (
        call "%USERPROFILE%\miniconda3\Scripts\activate.bat" demetify >nul 2>&1
        if !ERRORLEVEL! EQU 0 (
            echo [SUCCESS] Demetify is already installed and ready!
            echo.
            goto launch_program
        )
    )
)

echo [INFO] First-time setup required. Installing Demetify...
echo This will take 10-15 minutes depending on your internet connection.
echo.
pause

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] Conda already available
    goto setup_environment
)

REM Check common installation paths
if exist "%USERPROFILE%\miniconda3\Scripts\conda.exe" (
    echo [SUCCESS] Found Miniconda in user directory
    set "CONDA_PATH=%USERPROFILE%\miniconda3"
    goto setup_environment
)

echo [INFO] Installing Miniconda (Python environment)...
set "TEMP_DIR=%TEMP%\demetify_install"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"
cd /d "%TEMP_DIR%"

REM Download Miniconda installer
echo Downloading Miniconda installer...
powershell -Command "try { Invoke-WebRequest -Uri 'https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe' -OutFile 'Miniconda3-latest-Windows-x86_64.exe' } catch { exit 1 }"

if not exist "Miniconda3-latest-Windows-x86_64.exe" (
    echo [ERROR] Failed to download Miniconda installer
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo Installing Miniconda...
"Miniconda3-latest-Windows-x86_64.exe" /InstallationType=JustMe /RegisterPython=1 /S /D=%USERPROFILE%\miniconda3

REM Wait for installation
echo Waiting for installation to complete...
timeout /t 45 /nobreak >nul

REM Clean up
cd /d "%~dp0"
rmdir /s /q "%TEMP_DIR%" >nul 2>&1

set "CONDA_PATH=%USERPROFILE%\miniconda3"
echo [SUCCESS] Miniconda installed

:setup_environment
echo [INFO] Setting up Demetify environment...

REM Initialize conda
if defined CONDA_PATH (
    call "%CONDA_PATH%\Scripts\activate.bat" >nul 2>&1
) else (
    call conda activate base >nul 2>&1
)

REM Create environment
echo Creating Python environment...
conda create -n demetify python=3.9 -y >nul 2>&1

REM Activate environment
call conda activate demetify >nul 2>&1

REM Install dependencies
echo Installing AI libraries (this may take several minutes)...
echo - Installing PyTorch...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo   Falling back to CPU version...
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y >nul 2>&1
)

echo - Installing other dependencies...
pip install -r requirements.txt >nul 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to install some dependencies
    echo The system may still work, but some features might be limited
    pause
)

REM Create launcher
echo [INFO] Creating launcher...
echo @echo off > launch_demetify.bat
echo title Demetify - Running >> launch_demetify.bat
echo echo 🧠 Starting Demetify... >> launch_demetify.bat
echo echo. >> launch_demetify.bat
echo if exist "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" ^( >> launch_demetify.bat
echo     call "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" demetify >> launch_demetify.bat
echo ^) else ^( >> launch_demetify.bat
echo     call conda activate demetify >> launch_demetify.bat
echo ^) >> launch_demetify.bat
echo echo ✅ Environment activated >> launch_demetify.bat
echo echo 🚀 Launching Demetify frontend... >> launch_demetify.bat
echo echo    Access at: http://localhost:8501 >> launch_demetify.bat
echo echo    Press Ctrl+C to stop >> launch_demetify.bat
echo echo. >> launch_demetify.bat
echo streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0 >> launch_demetify.bat

REM Test installation
echo [INFO] Testing installation...
python test_system.py >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo [SUCCESS] ✅ Installation completed successfully!
) else (
    echo [WARNING] Installation completed with some issues, but should still work
)

echo.

:launch_program
echo ================================================================
echo 🚀 LAUNCHING DEMETIFY
echo ================================================================
echo.
echo The system will start in a few seconds...
echo Once started, open your web browser to: http://localhost:8501
echo.
echo To stop the system: Press Ctrl+C in the terminal window
echo.

REM Launch the program
if exist "launch_demetify.bat" (
    call launch_demetify.bat
) else (
    echo [ERROR] Launcher not found. Please run installation again.
    pause
    exit /b 1
)

REM Keep window open if there's an error
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Failed to start Demetify
    echo Please check the error messages above
    pause
)
