#!/usr/bin/env python3
"""
Simple Python launcher for Demetify - Windows Defender friendly alternative to batch files
"""

import subprocess
import sys
import webbrowser
import time
import os
from pathlib import Path

def main():
    print("🧠 Starting Demetify...")
    print("=" * 30)

    # Check if we're in the right directory
    if not Path("demetify_ncomms2022_app.py").exists():
        print("Error: demetify_ncomms2022_app.py not found!")
        print("Please run this script from the Demetify directory.")
        input("Press Enter to exit...")
        return

    try:
        # Install dependencies if needed
        print("Checking dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--user"],
                      check=False, capture_output=True)

        print("Starting Streamlit server...")
        print("The web interface will open automatically.")
        print("If it doesn't open, go to: http://localhost:8501")
        print("\nPress Ctrl+C to stop the server.")
        print("-" * 50)

        # Start Streamlit
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "demetify_ncomms2022_app.py",
            "--server.port", "8501",
            "--server.address", "0.0.0.0"
        ])

        # Wait a moment then open browser
        time.sleep(3)
        webbrowser.open("http://localhost:8501")

        # Wait for the process to complete
        process.wait()

    except KeyboardInterrupt:
        print("\n\nShutting down Demetify...")
        if 'process' in locals():
            process.terminate()
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()