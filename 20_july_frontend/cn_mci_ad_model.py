"""
CN/MCI/AD 3-Category Classification Model
Memory-efficient CNN for 3-way classification with 56% accuracy
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any, Tuple, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class MemoryEfficientCNN(nn.Module):
    """Memory-efficient CNN for CN/MCI/AD classification"""
    
    def __init__(self, num_classes=3):
        super(MemoryEfficientCNN, self).__init__()
        
        # Feature extraction layers
        self.features = nn.Sequential(
            # First block
            nn.Conv3d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=2, stride=2),
            nn.Dropout3d(0.1),
            
            # Second block
            nn.Conv3d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=2, stride=2),
            nn.Dropout3d(0.2),
            
            # Third block
            nn.Conv3d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        # Classifier
        self.classifier = nn.Sequential(
            nn.Dropout(0.5),
            nn.Linear(128 * 4 * 4 * 4, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = x.view(x.size(0), -1)
        x = self.classifier(x)
        return x

class CNMCIADClassifier:
    """3-category classifier for CN/MCI/AD classification"""
    
    def __init__(self, model_path: Optional[str] = None):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = MemoryEfficientCNN(num_classes=3)
        self.class_names = ['CN', 'MCI', 'AD']
        self.class_descriptions = {
            'CN': 'Cognitively Normal',
            'MCI': 'Mild Cognitive Impairment', 
            'AD': 'Alzheimer\'s Disease'
        }
        
        # Load model if path provided
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        else:
            logger.warning("No model loaded - using random weights")
    
    def load_model(self, model_path: str):
        """Load trained model weights"""
        try:
            state_dict = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(state_dict)
            self.model.to(self.device)
            self.model.eval()
            logger.info(f"Model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def preprocess_mri(self, mri_data: np.ndarray) -> torch.Tensor:
        """Preprocess MRI data for model input"""
        # Ensure 3D
        if mri_data.ndim == 4:
            mri_data = mri_data.squeeze()
        
        # Resize to expected input size (64x64x64)
        from scipy.ndimage import zoom
        target_size = (64, 64, 64)
        current_size = mri_data.shape
        zoom_factors = [target_size[i] / current_size[i] for i in range(3)]
        mri_data = zoom(mri_data, zoom_factors, order=1)
        
        # Normalize
        mri_data = (mri_data - np.mean(mri_data)) / (np.std(mri_data) + 1e-8)
        
        # Convert to tensor
        tensor = torch.FloatTensor(mri_data).unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
        return tensor.to(self.device)
    
    def predict(self, mri_data: np.ndarray) -> Dict[str, Any]:
        """Make prediction on MRI data"""
        try:
            # Preprocess
            input_tensor = self.preprocess_mri(mri_data)
            
            # Predict
            with torch.no_grad():
                outputs = self.model(input_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_class = torch.argmax(probabilities, dim=1).item()
            
            # Convert to numpy for easier handling
            probs = probabilities.cpu().numpy()[0]
            
            # Create results
            results = {
                'predicted_class': self.class_names[predicted_class],
                'predicted_class_description': self.class_descriptions[self.class_names[predicted_class]],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    self.class_names[i]: float(probs[i]) 
                    for i in range(len(self.class_names))
                },
                'class_probabilities_ordered': [
                    {
                        'class': self.class_names[i],
                        'description': self.class_descriptions[self.class_names[i]],
                        'probability': float(probs[i])
                    }
                    for i in np.argsort(probs)[::-1]  # Sort by probability descending
                ]
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get model information"""
        return {
            'model_type': 'Memory-Efficient CNN',
            'classes': self.class_names,
            'class_descriptions': self.class_descriptions,
            'accuracy': '56%',
            'training_data': 'NACC + ADNI datasets',
            'input_size': '64x64x64',
            'device': str(self.device)
        }

# Create a simple function for easy import
def create_classifier(model_path: Optional[str] = None) -> CNMCIADClassifier:
    """Create and return a CN/MCI/AD classifier"""
    return CNMCIADClassifier(model_path)
