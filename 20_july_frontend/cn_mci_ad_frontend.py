"""
Demetify - CN/MCI/AD 3-Category Classification Frontend
Professional radiologist-focused interface for 3-way dementia assessment
"""

import streamlit as st
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os
import logging
from typing import Optional, Dict, Any
import time

# Import nilearn for proper MRI visualization
try:
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map
    NILEARN_AVAILABLE = True
except ImportError:
    NILEARN_AVAILABLE = False
    st.warning("Nilearn not available. Install with: pip install nilearn")

# Import our custom model
from cn_mci_ad_model import create_classifier

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Demetify - CN/MCI/AD Assessment",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for Demetify branding
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    .prediction-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #2d5aa0;
        margin: 1rem 0;
    }
    .probability-bar {
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 0.5rem 0;
    }
    .probability-fill {
        height: 30px;
        display: flex;
        align-items: center;
        padding: 0 10px;
        color: white;
        font-weight: bold;
    }
    .cn-fill { background: #28a745; }
    .mci-fill { background: #ffc107; color: black; }
    .ad-fill { background: #dc3545; }
    .upload-section {
        border: 2px dashed #2d5aa0;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def load_mri_data(uploaded_file) -> Optional[np.ndarray]:
    """Load MRI data from uploaded file"""
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix='.nii') as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_file.flush()
            
            # Load with nibabel
            nii_img = nib.load(tmp_file.name)
            mri_data = nii_img.get_fdata()
            
            # Clean up
            os.unlink(tmp_file.name)
            
            return mri_data
            
    except Exception as e:
        st.error(f"Error loading MRI data: {e}")
        return None

def display_mri_slices(mri_data: np.ndarray, title: str = "MRI Visualization"):
    """Display MRI slices using matplotlib"""
    if mri_data.ndim == 4:
        mri_data = mri_data.squeeze()
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # Get middle slices
    mid_x, mid_y, mid_z = [s // 2 for s in mri_data.shape]
    
    # Sagittal view
    axes[0].imshow(mri_data[mid_x, :, :], cmap='gray', origin='lower')
    axes[0].set_title('Sagittal View')
    axes[0].axis('off')
    
    # Coronal view
    axes[1].imshow(mri_data[:, mid_y, :], cmap='gray', origin='lower')
    axes[1].set_title('Coronal View')
    axes[1].axis('off')
    
    # Axial view
    axes[2].imshow(mri_data[:, :, mid_z], cmap='gray', origin='lower')
    axes[2].set_title('Axial View')
    axes[2].axis('off')
    
    plt.tight_layout()
    return fig

def display_prediction_results(results: Dict[str, Any]):
    """Display prediction results with probabilities"""
    st.markdown('<div class="prediction-card">', unsafe_allow_html=True)
    
    # Main prediction
    predicted_class = results['predicted_class']
    confidence = results['confidence']
    description = results['predicted_class_description']
    
    st.markdown(f"### 🎯 Prediction: **{predicted_class}**")
    st.markdown(f"**{description}**")
    st.markdown(f"**Confidence:** {confidence:.1%}")
    
    st.markdown("### 📊 Classification Probabilities")
    
    # Display probability bars for all classes
    for class_info in results['class_probabilities_ordered']:
        class_name = class_info['class']
        prob = class_info['probability']
        desc = class_info['description']
        
        # Determine color class
        color_class = class_name.lower() + '-fill'
        
        st.markdown(f"**{class_name}** - {desc}")
        
        # Create probability bar
        bar_html = f"""
        <div class="probability-bar">
            <div class="probability-fill {color_class}" style="width: {prob*100}%;">
                {prob:.1%}
            </div>
        </div>
        """
        st.markdown(bar_html, unsafe_allow_html=True)
    
    st.markdown('</div>', unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧠 Demetify - CN/MCI/AD Assessment</h1>
        <p>Advanced 3-Category Dementia Classification System</p>
        <p><strong>University of Illinois at Urbana-Champaign</strong> | Project Lead: Prof. S. Seshadri</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize classifier
    @st.cache_resource
    def load_classifier():
        model_path = "memory_efficient_cnn_model.pth"
        if Path(model_path).exists():
            return create_classifier(model_path)
        else:
            st.warning("⚠️ Model file not found. Using demo mode.")
            return create_classifier()
    
    classifier = load_classifier()
    
    # Model information
    with st.expander("ℹ️ Model Information"):
        model_info = classifier.get_model_info()
        col1, col2 = st.columns(2)
        
        with col1:
            st.write(f"**Model Type:** {model_info['model_type']}")
            st.write(f"**Accuracy:** {model_info['accuracy']}")
            st.write(f"**Input Size:** {model_info['input_size']}")
        
        with col2:
            st.write(f"**Classes:** {', '.join(model_info['classes'])}")
            st.write(f"**Training Data:** {model_info['training_data']}")
            st.write(f"**Device:** {model_info['device']}")
    
    # File upload section
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.markdown("### 📁 Upload MRI Scan")
    st.markdown("Upload a T1-weighted MRI scan in NIfTI format (.nii or .nii.gz)")
    
    uploaded_file = st.file_uploader(
        "Choose MRI file",
        type=['nii', 'gz'],
        help="Upload T1-weighted MRI scan for CN/MCI/AD classification"
    )
    st.markdown('</div>', unsafe_allow_html=True)
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        
        # Load and display MRI data
        with st.spinner("Loading MRI data..."):
            mri_data = load_mri_data(uploaded_file)
        
        if mri_data is not None:
            # Display MRI visualization
            st.markdown("## 🖼️ MRI Visualization")
            fig = display_mri_slices(mri_data, f"MRI Scan: {uploaded_file.name}")
            st.pyplot(fig)
            
            # Make prediction
            st.markdown("## 🔬 AI Analysis")
            
            with st.spinner("Analyzing MRI scan..."):
                try:
                    results = classifier.predict(mri_data)
                    display_prediction_results(results)
                    
                except Exception as e:
                    st.error(f"Analysis failed: {e}")
                    logger.error(f"Prediction error: {e}")
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p><strong>Demetify</strong> - Advanced Dementia Assessment Tool</p>
        <p>⚠️ <em>For research purposes only. Not for clinical diagnosis.</em></p>
        <p>University of Illinois at Urbana-Champaign | 2025</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
